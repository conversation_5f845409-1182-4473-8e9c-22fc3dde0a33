{"name": "technischools-replatform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --check .", "format:fix": "prettier --write ."}, "dependencies": {"@headlessui/react": "^1.7.16", "@notionhq/client": "^2.2.11", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-tabs": "^1.0.4", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.12", "@tinymce/tinymce-react": "^5.0.1", "@types/node": "20.4.7", "@types/react": "18.2.18", "@types/react-dom": "18.2.7", "@upstash/ratelimit": "^2.0.3", "@upstash/redis": "^1.34.0", "dotenv": "^16.0.3", "eslint": "8.46.0", "eslint-config-next": "14.0.3", "fast-json-patch": "^3.1.1", "framer-motion": "^10.15.0", "htmr": "^1.0.2", "negotiator": "^0.6.3", "next": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.10.1", "sharp": "^0.32.6", "tailwind-merge": "^2.1.0"}, "devDependencies": {"autoprefixer": "^10.4.14", "postcss": "^8.4.27", "prettier": "3.0.1", "prettier-plugin-tailwindcss": "^0.4.1", "tailwindcss": "^3.3.3", "typescript": "5.1.6"}}