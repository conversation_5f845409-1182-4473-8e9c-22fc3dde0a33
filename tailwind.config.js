/** @type {import('tailwindcss').Config} */
module.exports = {
	content: ['./src/**/*.{js,ts,jsx,tsx}'],
	theme: {
		extend: {
			colors: {
				primary: '#3813c2',
				secondary: '#f20487'
			},
			backgroundImage: {
				footer: "url('/assets/images/footer-bg.webp')",
				headline: "url('/assets/images/headline-bg.webp')",
				shapes: "url('/assets/images/program-bg.svg')",
				stepper: "url('/assets/images/stepper-bg.svg')"
			},
			fontFamily: {
				rubik: 'var(--font-rubik)',
				roboto: 'var(--font-roboto)',
				termina: ['termina', 'sans-serif']
			},
			gridTemplateColumns: {
				fluid: 'repeat(auto-fit, minmax(15rem, 1fr))'
			},
			screens: {
				'2xs': '380px'
			}
		}
	},
	plugins: [
		require('prettier-plugin-tailwindcss'),
		require('@tailwindcss/typography'),
		require('@tailwindcss/forms')
	]
}
