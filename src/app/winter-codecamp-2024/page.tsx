import { cn } from '@/utils/style'
import Image from 'next/image'
import { Hi<PERSON>rrowR<PERSON>, HiEnvelope, HiPhone } from 'react-icons/hi2'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '../lubelska-wyspa-techni/tabs'

import TypeformTracker from '../components/TypeformTracker'
import './page.css'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './table'

export const metadata = {
	title: 'WINTER CODE CAMP',
	description: '8-klasisto! Weź udział w darmowych półkoloniach na feriach zimowych!',
	keywords: [
		'WINTER CODE CAMP',
		'8 klasa',
		'ferie zimowe',
		'szkoła',
		'sztuczna inteligencja',
		'cyberbezpieczeństwo',
		'wiedza',
		'edukacja',
		'Techni Schools',
		'programowanie',
		'programista'
	]
}

const SPEAKERS = [
	{
		fullName: '<PERSON><PERSON><PERSON>',
		bio: 'Doświadczony programista i etyczny hacker, który nie tylko posiada zaawansowane umiejętności techniczne. Jako założyciel Techni Schools, Mateusz skupia się na edukacji przyszłych talentów programistycznych. Partner w funduszu inwestycyjnym Techni Ventures, w którym aktywnie inwestuje w obiecujące startupy.',
		img: {
			path: 'https://dev.technischools.com/assets/link/656f316276361d134006b8a2',
			alt: 'Mateusz Kozłowski'
		}
	},
	{
		fullName: 'Sebastian Mysakowski',
		bio: 'Senior Frontend Developer @Northmill Bank AB, podcaster, wykładowca na Akademii Leona Koźmińskiego, autor kursów w wydawnictwie Helion oraz No Fluff Jobs, mentor oraz prowadzący warsztaty. Od początku związany z JavaScriptem. Doświadczenie zawodowe zdobywał zaczynając od startupów kończąc na firmach z indeksu S&P 500.',
		img: {
			path: 'https://dev.technischools.com/assets/link/656f316276361d134006b8a7',
			alt: 'Sebastian Mysakowski'
		}
	},
	{
		fullName: 'Jakub Leszcz',
		bio: 'Programista języka Python. Głównie zajmuje się uczeniem maszynowym oraz szeroko pojętą sztuczną inteligencją. Absolwent UMCS. Doświadczenie zawodowe zdobywa w projektach badawczo rozwojowych. Poza sferą zawodową interesuje się sportem, głównie piłką nożną oraz koszykówką.',
		img: {
			path: 'https://dev.technischools.com/assets/link/656f316276361d134006b8a8',
			alt: 'Jakub Leszcz'
		}
	},
	{
		fullName: 'Kacper Stefaniak',
		bio: 'Programista Machine Learning. Na co dzień rozwiązuje wyzwania związane z analizą danych i tworzeniem innowacyjnych projektów B+R. Po godzinach fascynują go gry komputerowe i rynki finansowe.',
		img: {
			path: 'https://dev.technischools.com/assets/link/656f316276361d134006b8aa',
			alt: 'Kacper Stefaniak'
		}
	},
	{
		fullName: 'Albert Błaziak',
		bio: 'Programista z szerokim zakresem zainteresowań, technologicznych i nie tylko. Doświadczenie zdobywał w projektach badawczo rozwojowych stosując najnowsze rozwiązania AI. Dziś jako Python Developer pracuje w szerokiej gamie projektów - od prostych aplikacji backendowych do zaawansowanych problemów wykorzystujących geolokalizację czy machine learning. Interesuje się także cyberbezpieczeństwem.',
		img: {
			path: 'https://dev.technischools.com/assets/link/656f316276361d134006b8a9',
			alt: 'Albert Błaziak'
		}
	},
	{
		fullName: 'Paweł Nowak',
		bio: 'Zawodowo programista w projektach komercyjnych opartych na AI, a także nauczyciel przedmiotów ścisłych. Absolwent UMCS. Doświadczenie zawodowe zdobył w licznych projektach B+R lub komercyjnych, gdzie wykorzystywał takie techniki jak np. analiza ludzkiego głosu, przetwarzanie języka naturalnego, prognozowanie w szeregach czasowych.',
		img: {
			path: 'https://dev.technischools.com/assets/link/656f316276361d134006b8a0',
			alt: 'Paweł Nowak'
		}
	},
	{
		fullName: 'Adam Kruk',
		bio: 'Unity developer pracujący przy grach AAA na komputery stacjonarne a od ponad roku pracuje przy grach mobilnych. Zajmuje się implementacją logik, UI, art, animacji oraz VFX czy SFX a także rozwiązań multiplayer przy użyciu Photon, Mirror czy połączenia z React. Pracuje dla największych polskich wydawców gier tj. PlayWay, Out Of the Ordinary. Współzałożyciel Workplays prężnie rozwijającego się job boardu dla ludzi z gamedevu.',
		img: {
			path: 'https://dev.technischools.com/assets/link/656f316276361d134006b8a1',
			alt: 'Adam Kruk'
		}
	},
	{
		fullName: 'Adrian Trąbka',
		bio: 'Programista aplikacji internetowych oraz desktopowych. W wolnym czasie zajmuje się tworzeniem muzyki elektronicznej. Adrian zdobył doświadczenie zawodowe pracując w startupach takich jak MeetraAI, Timeqube, czy ScatteredAI, gdzie skupiał się na rozwijaniu narzędzi wykorzystujących w głównej mierze sztuczną inteligencję. Oprócz tego był odpowiedzialny za rozwój projektów dla fundacji współpracujących z ministerstwem kultury.',
		img: {
			path: 'https://dev.technischools.com/assets/link/656f316276361d134006b8a3',
			alt: 'Adrian Trąbka'
		}
	},
	{
		fullName: 'Cezary Szymanowski',
		bio: 'Absolwent Katedry Lingwistyki Stosowanej na Uniwersytecie Marii Curie-Skłodowskiej w Lublinie. Nauczyciel języka angielskiego i niemieckiego. Lektor języka angielskiego w szkole Co Ludzie Powiedzą. Jego zainteresowania naukowe koncentrują się wokół dydaktyki nauczania języków obcych z uwzględnieniem nowoczesnych technologii oraz innowacji w procesie uczenia się.',
		img: {
			path: 'https://dev.technischools.com/assets/link/6572d1c9cac7efc77f062ed0',
			alt: 'Cezary Szymanowski'
		}
	},
	{
		fullName: 'Ania Rudnik',
		bio: 'Przygodę z szachami rozpoczęła w wieku 8 lat. Jako juniorka zdobyła wiele medali Mistrzostw Polski Juniorów. Za swój największy sukces uważa zdobycie srebrnego i brązowego medalu na Akademickich Mistrzostwach Europy, dzięki którym uzyskała mistrzowską klasę sportową. Aktualnie reprezentuje klub szachowy UKS Sparta Białołęką, który awansował do I ligi Drużynowych Mistrzostw Polski Seniorów. Od 2017 roku Ania posiada uprawnienia sędziowskie oraz instruktorskie. Największą satysfakcję dają jej postępy czynione przez swoich uczniów na każdym treningu.',
		img: {
			path: 'https://dev.technischools.com/assets/link/6572f06c694ac918520805d1',
			alt: 'Ania Rudnik'
		}
	},
	{
		fullName: 'Łukasz Dymek',
		bio: 'Absolwent studiów pedagogiki wczesnoszkolnej i przedszkolnej. Licencjonowany instruktor szachowy oraz sędzia szachowy klasy państwowej. Ceniony trener medalistów Mistrzostw Polski Juniorów. Łukasz jest również zwycięzcą wielu międzynarodowych turniejów szachowych. Właściciel Lubelskiej Akademii Szachowej.',
		img: {
			path: 'https://dev.technischools.com/assets/link/6572d1c9cac7efc77f062ed1',
			alt: 'Łukasz Dymek'
		}
	}
]

const STUDENTS = [
	{
		fullName: 'Marcel Geba',
		img: {
			alt: 'Marcel Geba',
			path: 'https://dev.technischools.com/assets/link/656f31d29572d346af0142a1'
		}
	},
	{
		fullName: 'Laura Strycharczuk',
		img: {
			alt: 'Laura Strycharczuk',
			path: 'https://dev.technischools.com/assets/link/656f316276361d134006b8a6'
		}
	},
	{
		fullName: 'Jakub Nowakowski',
		img: {
			alt: 'Jakub Nowakowski',
			path: 'https://dev.technischools.com/assets/link/65706ab843c4913fcb0abe40'
		}
	},
	{
		fullName: 'Agnieszka Płudowska',
		img: {
			alt: 'Agnieszka Płudowska',
			path: 'https://dev.technischools.com/assets/link/656f316276361d134006b8a5'
		}
	},
	{
		fullName: 'Tomasz Zając',
		img: {
			alt: 'Tomasz Zając',
			path: 'https://dev.technischools.com/assets/link/656f31d29572d346af0142a0'
		}
	},
	{
		fullName: 'Aleksander Haouem',
		img: {
			alt: 'Aleksander Haouem',
			path: 'https://dev.technischools.com/assets/link/6576cbd4117eadb0d40e6320'
		}
	},
	{
		fullName: 'Dawid Kupyn',
		img: {
			alt: 'Dawid Kupyn',
			path: 'https://dev.technischools.com/assets/link/6576d14a8cf7d799d10c0ef0'
		}
	}
]
const CITIES = [
	{
		img: 'https://dev.technischools.com/assets/link/654b7bcff2238c923e0eb7f0',
		name: 'Warszawa',
		date: '15-19 stycznia 2024',
		address: 'ul. Okopowa 59 (V piętro)',
		time: '9:00 - 16:00',
		dayTitles: [
			'Poniedziałek 15.01',
			'Wtorek 16.01',
			'Środa 17.01',
			'Czwartek 18.01',
			'Piątek 19.01'
		],
		agenda: {
			hours: [
				'8:30 - 9:00',
				'9:00 - 10:00',
				'10:00 - 10:30',
				'10:30 - 11:30',
				'11:30 - 11:45',
				'11:45 - 12:45',
				'12:45 - 13:45',
				'13:45 - 14:45',
				'14:45 - 15:00',
				'15:00 - 16:00',
				'16:00 - 16:30'
			],
			monday: [
				'Zbiórka uczestników',
				'Integracja',
				'Przerwa',
				'Python',
				'Przerwa',
				'Python',
				'Obiad',
				'Szachy',
				'Przerwa',
				'J. Angielski',
				'Zakończenie'
			],
			tuesday: [
				'Zbiórka uczestników',
				'Programowanie gier',
				'Przerwa',
				'Programowanie gier',
				'Przerwa',
				'Biały wywiad',
				'Obiad',
				'Biały wywiad',
				'Przerwa',
				'J. Angielski',
				'Zakończenie'
			],
			wednesday: [
				'Zbiórka uczestników',
				'Sztuczna inteligencja',
				'Przerwa',
				'Sztuczna inteligencja',
				'Przerwa',
				'Sztuczna inteligencja',
				'Obiad',
				'Sztuczna inteligencja',
				'Przerwa',
				'J. Angielski',
				'Zakończenie'
			],
			thursday: [
				'Zbiórka uczestników',
				'Strony WWW',
				'Przerwa',
				'Strony WWW',
				'Przerwa',
				'Strony WWW',
				'Obiad',
				'Strony WWW',
				'Przerwa',
				'J. Angielski',
				'Zakończenie'
			],
			friday: [
				'Zbiórka uczestników',
				'AIStack',
				'Przerwa',
				'AIStack',
				'Przerwa',
				'J. Angielski',
				'Obiad',
				'Tomasz Wróblewski',
				'Zakończenie'
			]
		}
	},
	{
		img: 'https://dev.technischools.com/assets/link/656f316276361d134006b8ad',
		name: 'Wrocław',
		date: '22-26 stycznia 2024',
		address: 'ul. Bema 3 (I piętro)',
		time: '9:00 - 16:00',
		dayTitles: [
			'Poniedziałek 22.01',
			'Wtorek 23.01',
			'Środa 24.01',
			'Czwartek 25.01',
			'Piątek 26.01'
		],
		agenda: {
			hours: [
				'8:30 - 9:00',
				'9:00 - 10:00',
				'10:00 - 10:30',
				'10:30 - 11:30',
				'11:30 - 11:45',
				'11:45 - 12:45',
				'12:45 - 13:45',
				'13:45 - 14:45',
				'14:45 - 15:00',
				'15:00 - 16:00',
				'16:00 - 16:30'
			],
			monday: [
				'Zbiórka uczestników',
				'Integracja',
				'Przerwa',
				'Python',
				'Przerwa',
				'Python',
				'Obiad',
				'Szachy',
				'Przerwa',
				'J. Angielski',
				'Zakończenie'
			],
			tuesday: [
				'Zbiórka uczestników',
				'Programowanie gier',
				'Przerwa',
				'Programowanie gier',
				'Przerwa',
				'Biały wywiad',
				'Obiad',
				'Biały wywiad',
				'Przerwa',
				'J. Angielski',
				'Zakończenie'
			],
			wednesday: [
				'Zbiórka uczestników',
				'Sztuczna inteligencja',
				'Przerwa',
				'Sztuczna inteligencja',
				'Przerwa',
				'Sztuczna inteligencja',
				'Obiad',
				'Sztuczna inteligencja',
				'Przerwa',
				'J. Angielski',
				'Zakończenie'
			],
			thursday: [
				'Zbiórka uczestników',
				'Strony WWW',
				'Przerwa',
				'Strony WWW',
				'Przerwa',
				'Strony WWW',
				'Obiad',
				'Strony WWW',
				'Przerwa',
				'J. Angielski',
				'Zakończenie'
			],
			friday: [
				'Zbiórka uczestników',
				'AIStack',
				'Przerwa',
				'AIStack',
				'Przerwa',
				'J. Angielski',
				'Obiad',
				'Tomasz Wróblewski',
				'Zakończenie'
			]
		}
	},
	{
		img: 'https://dev.technischools.com/assets/link/656f316276361d134006b8ab',
		name: 'Lublin',
		date: '05-09 luty 2024',
		address: 'ul. Narutowicza 55b (II piętro)',
		time: '9:00 - 16:00',
		dayTitles: [
			'Poniedziałek 05.02',
			'Wtorek 06.02',
			'Środa 07.02',
			'Czwartek 08.02',
			'Piątek 09.02'
		],
		agenda: {
			hours: [
				'8:30 - 9:00',
				'9:00 - 10:00',
				'10:00 - 10:20',
				'10:20 - 11:20',
				'11:20 - 11:30',
				'11:30 - 12:30',
				'12:30 - 13:45',
				'13:45 - 14:45',
				'14:45 - 15:00',
				'15:00 - 16:00',
				'16:00 - 16:30'
			],
			monday: [
				'Zbiórka uczestników',
				'Integracja',
				'Przerwa',
				'Szachy',
				'Przerwa',
				'Szachy',
				'Obiad',
				'Biały wywiad',
				'Przerwa',
				'Biały wywiad',
				'Zakończenie'
			],
			tuesday: [
				'Zbiórka uczestników',
				'Python',
				'Przerwa',
				'Hackowanie',
				'Przerwa',
				'Hackowanie',
				'Obiad',
				'J. Angielski',
				'Przerwa',
				'J. Angielski',
				'Zakończenie'
			],
			wednesday: [
				'Zbiórka uczestników',
				'J. Angielski',
				'Przerwa',
				'J. Angielski',
				'Przerwa',
				'Python',
				'Obiad',
				'Python',
				'Przerwa',
				'Python',
				'Zakończenie'
			],
			thursday: [
				'Zbiórka uczestników',
				'Strony WWW',
				'Przerwa',
				'Strony WWW',
				'Przerwa',
				'Strony WWW',
				'Obiad',
				'Sztuczna inteligencja',
				'Przerwa',
				'Sztuczna inteligencja',
				'Zakończenie'
			],
			friday: [
				'Zbiórka uczestników',
				'Sztuczna inteligencja',
				'Przerwa',
				'Szachy',
				'Przerwa',
				'AI Stack',
				'Obiad',
				'AI Stack',
				'Przerwa',
				'Prezentacja o szkole',
				'Zakończenie'
			]
		}
	}
]
function isRowEmpty(city: string, rowIndex: number) {
	const agenda = CITIES.find((c) => c.name === city)?.agenda
	if (!agenda) return false
	const days = Object.keys(agenda).filter((key) => key !== 'hours')
	return days
		.slice(0, -1)
		.every((day) => agenda[day as keyof typeof agenda][rowIndex] === 'Przerwa')
}

export default function LubelskaWyspaPage() {
	return (
		<>
			<div
				aria-hidden
				className="snowflakes pointer-events-none fixed inset-0 z-20 -translate-y-32 [--snowflake-color:#C8E9EFaa] max-md:-translate-y-64"
			>
				{[...Array(84)].map((_, i) => (
					<i key={i} className="snowflake" />
				))}
			</div>
			<div className="bg-gradient-to-r from-sky-200 via-blue-200 to-blue-400">
				<Section className="flex h-full items-center justify-between md:flex-row md:pb-0 md:pt-24">
					<div className="md:pb-24">
						<hgroup className="space-y-6 max-md:text-center">
							<h1 className="font-rubik text-4xl font-semibold text-slate-900 [text-shadow:_0_1.5px_0_rgb(0_0_0_/_20%)] [text-wrap:balance] md:text-7xl">
								WINTER CODE CAMP
							</h1>
							<p className="text-slate-700 [text-wrap:balance] md:text-lg">
								8-klasisto! Weź udział w darmowych półkoloniach na feriach zimowych!
							</p>
						</hgroup>
						<a
							href="#signup"
							className="btn-lg group mt-8 flex rounded-xl border-none bg-gradient-to-b from-[#f23da1] to-[#f20487] py-3 text-white shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] [text-shadow:_0_1px_0_rgb(0_0_0_/_20%)] hover:brightness-105 active:scale-[0.98] max-md:mx-auto sm:text-lg"
						>
							Zapisz się
							<HiArrowRight className="ml-2 h-4 w-4 transition-transform [stroke-width:2px] group-hover:translate-x-1" />
						</a>
					</div>
					<div className="relative h-full overflow-hidden max-sm:hidden">
						<Image
							src={'https://dev.technischools.com/assets/link/6576c15940e55c732a0668c0'}
							alt="Lubelska WySPA Techni"
							width={400}
							height={400}
							objectFit="cover"
						/>
					</div>
				</Section>
			</div>
			<Section id="wydarzenie">
				<SectionTitle>Wydarzenie</SectionTitle>
				<div className="max-w-prose space-y-5 font-normal text-zinc-700 sm:text-lg">
					<p>
						Zapraszamy na bezpłatne ferie zimowe z
						<span className="font-rubik font-medium text-zinc-950"> Winter Code Camp 2024! </span>
					</p>
					<p>
						Przeżyj 5 dni niesamowitych przygód w świecie technologii i nauki. Nasz camp to nie
						tylko nauka, ale i zabawa. To idealne miejsce dla młodych pasjonatów technologii
						uczących się w 8 klasie szkoły podstawowej.
					</p>
					<p>
						<span className="font-rubik font-medium text-zinc-950">W programie między innymi:</span>
						<ul className="list-disc space-y-2 pl-5">
							<li>
								<span className="font-rubik font-medium text-zinc-950">
									AI & Cyberbezpieczeństwo:
								</span>{' '}
								Poznaj tajniki sztucznej inteligencji i zasady cyberbezpieczeństwa. Stań się
								mistrzem w ochronie danych!
							</li>
							<li>
								<span className="font-rubik font-medium text-zinc-950">
									Programowanie w Pythonie:
								</span>{' '}
								Odkryj język Python i naucz się tworzyć własne programy. Twoja pierwsza aplikacja
								czeka!
							</li>
							<li>
								<span className="font-rubik font-medium text-zinc-950">Tworzenie Gier:</span> Wejdź
								do świata gier komputerowych. Naucz się, jak tworzyć własne gry i dzielić się nimi
								ze światem.
							</li>
							<li>
								<span className="font-rubik font-medium text-zinc-950">OSINT:</span> Zostań
								detektywem cyfrowym. Dowiedz się, jak zbierać i analizować informacje z otwartych
								źródeł.
							</li>
							<li>
								<span className="font-rubik font-medium text-zinc-950">Szachy:</span> Rozwijaj
								strategiczne myślenie, ucząc się gry w szachy od doświadczonych trenerów.
							</li>
							<li>
								<span className="font-rubik font-medium text-zinc-950">Język Angielski:</span>{' '}
								Popraw swoje umiejętności językowe, uczestnicząc w zajęciach z autorskim modelem
								nauki słówek.
							</li>
						</ul>
					</p>
					<p>
						Miejsca są ograniczone, więc nie zwlekaj! Zarejestruj się teraz aby zapewnić sobie
						miejsce na Winter Code Camp 2024
					</p>
					<a
						href="#signup"
						className="font-rubik font-medium text-primary underline underline-offset-2"
					>
						Zapisz się już dziś!
					</a>
				</div>
			</Section>

			<Section id="agenda">
				<SectionTitle>Agenda</SectionTitle>
				<Tabs defaultValue="Warszawa">
					<TabsList className="bg-slate-200 max-sm:w-full">
						<TabsTrigger className="max-sm:w-full" value="Warszawa">
							Warszawa
						</TabsTrigger>
						<TabsTrigger className="max-sm:w-full" value="Wrocław">
							Wrocław
						</TabsTrigger>
						<TabsTrigger className="max-sm:w-full" value="Lublin">
							Lublin
						</TabsTrigger>
					</TabsList>
					<div className="mt-2 h-full rounded-lg border bg-white p-4 shadow-md ring-1 ring-black/5">
						{CITIES.map((city) => (
							<TabsContent value={city.name}>
								<Table>
									<TableHeader>
										<TableRow>
											{Object.keys(city.agenda).map((day, idx) => (
												<TableHead
													className={cn(
														'font-semibold uppercase text-slate-700',
														day === 'hours' && 'min-w-[10rem]'
													)}
												>
													{idx === 0 ? '' : city.dayTitles[idx - 1]}
												</TableHead>
											))}
										</TableRow>
									</TableHeader>
									<TableBody>
										{city.agenda.hours.map((_, i) => (
											<TableRow key={i} className="even:bg-slate-100">
												{Object.keys(city.agenda).map((day) => (
													<TableCell
														className={cn(
															'text-base',
															day === 'hours' && 'font-rubik font-semibold tabular-nums',
															isRowEmpty(city.name, i) && 'py-2 text-slate-500',
															city.agenda[day as keyof typeof city.agenda][i] === 'Zakończenie' &&
																'text-inherit'
														)}
													>
														{city.agenda[day as keyof typeof city.agenda][i]}
													</TableCell>
												))}
											</TableRow>
										))}
									</TableBody>
								</Table>
							</TabsContent>
						))}
					</div>
				</Tabs>
			</Section>
			<Section id="prelegenci">
				<SectionTitle>Prelegenci</SectionTitle>
				<div className="flex h-full flex-wrap gap-8">
					{SPEAKERS.map((prelegent) => (
						<div className="h-fit w-full max-w-xl rounded-xl p-4 shadow-md ring-1 ring-black/5 md:h-96">
							<Image
								src={prelegent.img.path}
								alt={prelegent.img.alt}
								width={160}
								height={160}
								className="aspect-square rounded-md object-cover object-top shadow-md ring-1 ring-black/5 max-sm:mb-4 max-sm:w-full sm:float-left sm:mr-4"
							/>
							<h4 className="mb-2 font-rubik text-xl font-medium">{prelegent.fullName}</h4>
							<div className="space-y-3 leading-8">
								{prelegent.bio.split('\n').map((paragraph, i) => (
									<p key={i} className="text-zinc-700">
										{paragraph}
									</p>
								))}
							</div>
						</div>
					))}
				</div>
			</Section>
			<Section id="uczniowie">
				<SectionTitle hideRectangle>Prelegenci wśród uczniów</SectionTitle>
				<div className="flex h-full flex-wrap gap-8">
					{STUDENTS.map((student) => (
						<div className="h-fit w-full max-w-xs rounded-xl p-4 shadow-md ring-1 ring-black/5">
							<Image
								src={student.img.path}
								alt={student.img.alt}
								width={160}
								height={160}
								className="mb-4 aspect-square w-full rounded-md object-cover object-top shadow-md ring-1 ring-black/5"
							/>
							<h4 className="font-rubik text-xl font-medium">{student.fullName}</h4>
						</div>
					))}
				</div>
			</Section>
			<Section id="signup">
				<SectionTitle>Zapisz się!</SectionTitle>

				{/* <div
					data-tf-live="01HGX4P049NTQNP90SB9ZA84WD"
					className="mx-auto h-auto w-[800px] max-w-full rounded-xl bg-white p-3 shadow-md ring-1 ring-black/5"
				></div>
				<TypeformTracker formName="winter-codecamp-2024" /> */}
				<p className="text-center text-slate-700">
					Rekrutacja na Winter Code Camp 2024 została zakończona.
				</p>
			</Section>
			<div className="w-full bg-gradient-to-r from-sky-200 via-blue-200 to-blue-400 shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)]">
				<Section id="signup">
					<SectionTitle hideRectangle>Data i miejsce</SectionTitle>
					<div className="flex items-center justify-around gap-8 text-slate-900 max-sm:flex-col">
						{CITIES.map((city) => (
							<div className="flex flex-col items-center justify-center">
								<div className="relative mb-8 aspect-square w-48 overflow-hidden rounded-2xl md:w-72">
									<Image
										alt={`Zdjęcie ${city.name}`}
										src={city.img}
										layout="fill"
										objectFit="cover"
									/>
								</div>
								<h2 className="mb-4 font-rubik text-3xl font-medium">{city.name}</h2>
								<p className="mb-2">{city.date}</p>
								<p className="mb-2">{city.address}</p>
								<p className="mb-2">{city.time}</p>
							</div>
						))}
					</div>
				</Section>
			</div>
			<Section>
				<SectionTitle>Kontakt</SectionTitle>
				<div className="flex items-center justify-between gap-8 max-xl:flex-col">
					<div className="flex w-full flex-col items-center justify-center gap-x-8 gap-y-2 md:flex-row">
						<span className="font-rubik text-lg font-medium text-zinc-950">Zadzwon do nas:</span>

						<a
							href="tel:+48721221299"
							className="btn-lg group flex rounded-xl border-none bg-gradient-to-b from-blue-200 to-blue-300 py-3 text-slate-900 shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] [text-shadow:_0_1px_0_rgb(0_0_0_/_20%)] hover:brightness-105 active:scale-[0.98] max-md:mx-auto max-md:w-full sm:text-lg"
							aria-label="{`Call ${data.phone}`}"
						>
							<span className="mr-1 grid h-5 w-5 place-items-center">
								<HiPhone className="min-w-full" />
							</span>
							+48 721 221 299
						</a>
					</div>
					<div className="flex w-full flex-col items-center justify-center gap-x-8 gap-y-2 md:flex-row">
						<span className="font-rubik text-lg font-medium text-zinc-950">lub napisz:</span>
						<a
							href="mailto:<EMAIL>"
							className="btn-lg group flex rounded-xl border-none bg-gradient-to-b from-blue-200 to-blue-300 py-3 text-slate-900 shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] [text-shadow:_0_1px_0_rgb(0_0_0_/_20%)] hover:brightness-105 active:scale-[0.98] max-md:mx-auto max-md:w-full sm:text-lg"
							aria-label="Email <EMAIL>"
						>
							<span className="mr-1 grid h-5 w-5 place-items-center">
								<HiEnvelope className="min-w-full" />
							</span>
							<EMAIL>
						</a>
					</div>
					<div className="flex w-full flex-col items-center justify-center gap-x-8 gap-y-2 md:flex-row">
						<span className="font-rubik text-lg font-medium text-zinc-950">Zobacz też:</span>
						<a
							href="https://technischools.com/regulamin-wcc-24"
							className="btn-lg group flex rounded-xl border-none bg-gradient-to-b from-blue-200 to-blue-300 py-3 text-slate-900 shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] [text-shadow:_0_1px_0_rgb(0_0_0_/_20%)] hover:brightness-105 active:scale-[0.98] max-md:mx-auto max-md:w-full sm:text-lg"
							aria-label="Email <EMAIL>"
						>
							Regulamin
						</a>
					</div>
				</div>
			</Section>
		</>
	)
}

function Section({
	children,
	className,

	...props
}: React.HTMLAttributes<HTMLDivElement> & {
	children: React.ReactNode
	className?: string
}) {
	return (
		<section className={cn('container mx-auto px-4 py-12 lg:px-12', className)} {...props}>
			{children}
		</section>
	)
}

function SectionTitle({
	children,
	className,
	hideRectangle = false,
	...props
}: React.HTMLAttributes<HTMLDivElement> & {
	children: React.ReactNode
	className?: string
	hideRectangle?: boolean
}) {
	return (
		<div
			className={cn('relative mb-8 flex h-20 w-full items-center text-slate-900', className)}
			{...props}
		>
			{!hideRectangle && (
				<div className="absolute -left-1/2 top-0 h-full w-full rounded-3xl bg-gradient-to-b from-blue-200 to-blue-300 shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] sm:-left-2/3"></div>
			)}
			<h2 className="relative z-[2] font-rubik text-2xl font-bold [text-shadow:_0_1.5px_0_rgb(0_0_0_/_20%)] sm:text-3xl lg:text-5xl">
				{children}
			</h2>
		</div>
	)
}
