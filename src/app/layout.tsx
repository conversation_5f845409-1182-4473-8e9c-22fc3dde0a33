import '@/app/globals.css'
import Header from '@components/Header'
import Footer from '@components/Footer'
import { Roboto, Rubik } from 'next/font/google'
import { i18n } from '@/utils/i18n'
import { cn } from '@/utils/style'
import AcceptCookies from '@components/AcceptCookies'
import { getCookiePopup, getCustomPagesInfo, getHeader, getMetaData } from '@/utils/api'
import { ScrollUp } from '@components/ScrollUp'
import type { Metadata } from 'next'
import Script from 'next/script'
import { getAssetPath } from '@/utils/assets'

const roboto = Roboto({
	subsets: ['latin-ext'],
	weight: ['300', '400', '500', '700'],
	variable: '--font-roboto'
})

const rubik = Rubik({
	subsets: ['latin-ext'],
	weight: ['300', '400', '500', '700'],
	variable: '--font-rubik'
})

export async function generateMetadata(): Promise<Metadata> {
	const locale = i18n.defaultLocale
	const { title, template, description, favicon, ogImage, keywords } = await getMetaData()
	return {
		title: {
			default: title,
			template
		},
		description,
		keywords,
		icons: {
			shortcut: getAssetPath(favicon.path)
		},
		openGraph: {
			title,
			description,
			type: 'website',
			locale,
			url: process.env.NEXT_PUBLIC_WEBSITE_URL,
			images: [
				{
					url: getAssetPath(ogImage.path)
				}
			]
		}
	}
}

declare global {
	interface Window {
		dataLayer: Record<string, any>[]
		gtag: (...args: any[]) => void
	}
}

export default async function RootLayout({ children }: { children: React.ReactNode }) {
	const [cookieData, headerData, customPagesInfo] = await Promise.all([
		getCookiePopup(),
		getHeader(),
		getCustomPagesInfo()
	])
	const locale = i18n.defaultLocale
	return (
		<html lang={locale} className={cn('font-roboto', roboto.variable, rubik.variable)}>
			<head />
			<body>
				<Script id="gtag-consent-default" strategy="beforeInteractive">
					{`window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}gtag('consent','default',{ad_storage:'denied',analytics_storage:'denied',ad_user_data:'denied',ad_personalization:'denied',wait_for_update:500});
`}
				</Script>
				<Script id="gtag-js" strategy="afterInteractive" async src={`https://www.googletagmanager.com/gtag/js?id=G-4K6X2RR0BJ`} />
				<Script id="gtag-config" strategy="afterInteractive">
					{`gtag('js', new Date());gtag('config','G-4K6X2RR0BJ');`}
				</Script>
				<Script id="gtm-init" strategy="afterInteractive">
					{`window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}

// Initialize GTM with consent mode
(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl+'&gtm_auth=&gtm_preview=&gtm_cookies_win=x';f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','GTM-57KCKZK');`}
				</Script>
				{/* Meta Pixel Code */}
				<Script>
					{`(function(f,b,e,v,n,t,s)
				  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
				  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
				  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
				  n.queue=[];t=b.createElement(e);t.async=!0;
				  t.src=v;s=b.getElementsByTagName(e)[0];
				  s.parentNode.insertBefore(t,s)})(window, document,'script',
				  'https://connect.facebook.net/en_US/fbevents.js');
				  fbq('init', '263528291338198');
				  fbq('track', 'PageView');
				  fbq('track', 'AddPaymentInfo');
				  fbq('track', 'AddToCart');
				  fbq('track', 'AddToWishlist');
				  fbq('track', 'CompleteRegistration');
				  fbq('track', 'Contact');
				  fbq('track', 'CustomizeProduct');
				  fbq('track', 'Donate');
				  fbq('track', 'FindLocation');
				  fbq('track', 'InitiateCheckout');
				  fbq('track', 'Lead');
				  fbq('track', 'Purchase');
				  fbq('track', 'Schedule');
				  fbq('track', 'Search');
				  fbq('track', 'SubmitApplication');
				  fbq('track', 'ViewContent');`}
				</Script>

				<noscript>
					<img
						height="1"
						width="1"
						className="hidden"
						src="https://www.facebook.com/tr?id=263528291338198&ev=PageView&noscript=1"
					/>
				</noscript>
				{/* End Meta Pixel Code */}
				<Header
					data={headerData}
					customPages={[
						{
							slug: 'lubelska-wyspa-techni',
							transparentHeader: false,
							noMarginBottom: true
						},
						{
							slug: 'kurs-future-ai-2023',
							transparentHeader: false,
							noMarginBottom: true
						},
						{
							slug: 'winter-codecamp-2024',
							transparentHeader: false,
							noMarginBottom: true
						},
						{
							slug: 'spektakl',
							transparentHeader: true,
							noMarginBottom: false
						},
						...(customPagesInfo ?? []).map((page) => ({
							slug: page.slug,
							transparentHeader: page.transparentHeader ?? false,
							noMarginBottom: page.pageSchema ? !page.transparentHeader : false
						}))
					]}
				/>
				<main>{children}</main>
				<AcceptCookies data={cookieData} />
				<ScrollUp />
				<Footer />
			</body>
		</html>
	)
}
