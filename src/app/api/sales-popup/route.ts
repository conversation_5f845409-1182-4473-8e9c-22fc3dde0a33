import { NextResponse } from 'next/server'
import { Client } from '@notionhq/client'
import { Ratelimit } from '@upstash/ratelimit'
import { Redis } from '@upstash/redis'

async function AddRecordToCRM(
	text: string,
	data: {
		phonenumber: string
		city: string
		email: string
		fullName: string
	}
) {
	try {
		const notion_key = process.env.NOTION_KEY
		const notion = new Client({ auth: notion_key })
		const databaseId = process.env.NOTION_DATABASE_ID

		console.log(databaseId)

		const response = await notion.pages.create({
			parent: { database_id: databaseId! },
			properties: {
				title: {
					title: [
						{
							text: {
								content: text
							}
						}
					]
				},
				Status: {
					select: {
						name: 'Lead'
					}
				},
				Full_name: {
					rich_text: [
						{
							text: {
								content: data.fullName
							}
						}
					]
				},
				Phone: {
					phone_number: data.phonenumber
				},
				Campaign: {
					select: {
						name: 'sales technischools.com'
					}
				},
				City: {
					select: {
						name: data.city
					}
				},
				Email: {
					email: data.email
				}
			}
		})
		// console.log(response)
		console.log('Success! Entry added.')
	} catch (error: any) {
		console.error(error.body)
	}
}

const ratelimit = new Ratelimit({
	redis: Redis.fromEnv(),
	limiter: Ratelimit.slidingWindow(3, '1 h'),
	analytics: true,
	prefix: '@upstash/ratelimit'
})

function getCombinedIdentifier(ip: string, userAgent: string, platform: string) {
	return `${ip}-${userAgent}-${platform}`
}

export async function POST(request: Request) {
	const ip = request.headers.get('x-forwarded-for') || 'anonymous'
	const userAgent = request.headers.get('user-agent') || 'unknown'
	const platform = request.headers.get('sec-ch-ua-platform') || 'unknown'

	const identifier = getCombinedIdentifier(ip, userAgent, platform)
	console.log(`Rate-limiting identifier: ${identifier}`)

	const { success } = await ratelimit.limit(identifier)
	if (!success) {
		return NextResponse.json({ ok: false }, { status: 429 })
	}
	const data = await request.json()
	const { phonenumber, city, email, fullName } = data
	console.log(data)
	if (!phonenumber || !city || phonenumber.length < 9) return NextResponse.json({ ok: false })
	const generatedText = `Zapis ze strony sprzedażowej technischools.com \nImię i nazwisko: ${fullName} \nMiasto: ${city} \nTelefon: ${phonenumber} \nEmail: ${email}`
	const url = new URL(
		'https://api.telegram.org/bot5082109821:AAF3O5Deve1T7pgFiEYpsSUyuZ1dXLBzQ6s/sendMessage'
	)
	url.searchParams.set('chat_id', '-733321960')
	url.searchParams.set('text', generatedText)
	console.log(url)
	const response = await fetch(url)
	const telegramResponse = await response.json()
	await AddRecordToCRM(`Zapis ze strony sprzedażowej technischools.com`, data)
	return NextResponse.json({ ok: telegramResponse.ok })
}
