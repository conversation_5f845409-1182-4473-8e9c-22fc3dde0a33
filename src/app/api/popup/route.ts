import { NextResponse } from 'next/server'
import { Client } from '@notionhq/client'
import { Ratelimit } from '@upstash/ratelimit'
import { Redis } from '@upstash/redis'

async function AddRecordToCRM(text: string, phone: string, city: string) {
	try {
		const notion_key = process.env.NOTION_KEY
		const notion = new Client({ auth: notion_key })
		const databaseId = process.env.NOTION_DATABASE_ID

		console.log(databaseId)

		const response = await notion.pages.create({
			parent: { database_id: databaseId! },
			properties: {
				title: {
					title: [
						{
							text: {
								content: text
							}
						}
					]
				},
				Status: {
					select: {
						name: 'Lead'
					}
				},
				Phone: {
					phone_number: phone
				},
				Campaign: {
					select: {
						name: 'technischools.com'
					}
				},
				City: {
					select: {
						name: city
					}
				}
			}
		})
		// console.log(response)
		console.log('Success! Entry added.')
	} catch (error: any) {
		console.error(error.body)
	}
}

const ratelimit = new Ratelimit({
	redis: Redis.fromEnv(),
	limiter: Ratelimit.slidingWindow(3, '1 h'),
	analytics: true,
	prefix: '@upstash/ratelimit'
})

function getCombinedIdentifier(ip: string, userAgent: string, platform: string) {
	return `${ip}-${userAgent}-${platform}`
}

export async function POST(request: Request) {
	const ip = request.headers.get('x-forwarded-for') || 'anonymous'
	const userAgent = request.headers.get('user-agent') || 'unknown'
	const platform = request.headers.get('sec-ch-ua-platform') || 'unknown'

	const identifier = getCombinedIdentifier(ip, userAgent, platform)
	console.log(`Rate-limiting identifier: ${identifier}`)

	const { success } = await ratelimit.limit(identifier)
	if (!success) {
		return NextResponse.json({ ok: false }, { status: 429 })
	}
	const data = await request.json()
	const { phonenumber, city } = data

	if (!phonenumber || !city || phonenumber.length < 9) return NextResponse.json({ ok: false })

	const url = new URL(`https://api.telegram.org/${process.env.TELEGRAM_AUTH_KEY}/sendMessage`)
	url.searchParams.set('chat_id', process.env.TELEGRAM_CHAT_ID!)
	url.searchParams.set('text', `Nowy zapis ze strony technischools.com ${city}: ${phonenumber}`)
	const response = await fetch(url)
	const telegramResponse = await response.json()
	//console.log('Telegram response', telegramResponse)
	await AddRecordToCRM(`Nowy zapis ze strony technischools.com`, phonenumber, city)
	return NextResponse.json({ ok: telegramResponse.ok })
}
