export async function POST(request: Request) {
	const data = await request.json()
	const { prompt, schemaId } = data
	if (!prompt || !schemaId) return new Response(null, { status: 400 })
	if (!process.env.PB_AI_TOKEN) return new Response(null, { status: 401 })

	const response = await fetch(`${process.env.PB_API_URL}/continue`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'x-api-key': process.env.PB_AI_TOKEN
		},
		body: JSON.stringify({
			prompt,
			schemaId
		})
	})

	const responseData = await response.json()
	return new Response(JSON.stringify(responseData), { status: response.status })
}
