export async function POST(request: Request) {
	const data = await request.json()
	const { schema } = data
	if (!schema) return new Response(null, { status: 400 })
	if (!process.env.PB_AI_TOKEN) return new Response(null, { status: 401 })

	const response = await fetch(`${process.env.PB_API_URL}/schema`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json',
			'x-api-key': process.env.PB_AI_TOKEN
		},
		body: JSON.stringify({
			schema
		})
	})
	const responseData = await response.json()
	return new Response(JSON.stringify(responseData), { status: response.status })
}
