import { cookies } from 'next/headers'

export async function GET() {
	const cookieStore = cookies()
	const cookieSettings = cookieStore.get('cookie_settings')
	return new Response(
		JSON.stringify({
			cookieSettings: cookieSettings ? JSON.parse(cookieSettings.value) : null,
			cookieNoticeAccepted: cookieSettings ? true : false
		}),
		{
			status: 200,
			headers: { 'Content-Type': 'application/json' }
		}
	)
}

export async function POST(request: Request) {
	const { settings } = await request.json()
	const oneYear = 365 * 24 * 60 * 60

	return new Response('Settings saved!', {
		status: 200,
		headers: {
			'Set-Cookie': `cookie_settings=${JSON.stringify(
				settings
			)}; Max-Age=${oneYear}; Path=/; SameSite=Strict; Secure`
		}
	})
}
