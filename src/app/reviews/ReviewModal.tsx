'use client'
import { Review as ReviewType } from '@/types'
import { getAssetPath } from '@/utils/assets'
import { Dialog, Transition } from '@headlessui/react'
import Image from 'next/image'
import { Fragment } from 'react'
import { HiOutlinePlay, HiVideoCamera, HiXMark } from 'react-icons/hi2'

interface ReviewModalProps {
	isOpen: boolean
	onClose: () => void
	review: ReviewType
}

export const ReviewModal = ({ isOpen, onClose, review }: ReviewModalProps) => {
	const { fullName, position, review: reviewText, img, videoUrl } = review

	return (
		<Transition appear show={isOpen} as={Fragment}>
			<Dialog as="div" className="relative z-50" onClose={onClose}>
				<Transition.Child
					as={Fragment}
					enter="ease-out duration-300"
					enterFrom="opacity-0"
					enterTo="opacity-100"
					leave="ease-in duration-200"
					leaveFrom="opacity-100"
					leaveTo="opacity-0"
				>
					<div className="fixed inset-0 bg-black/50" />
				</Transition.Child>

				<div className="fixed inset-0 overflow-y-auto">
					<div className="flex min-h-full items-center justify-center p-4 text-center">
						<Transition.Child
							as={Fragment}
							enter="ease-out duration-300"
							enterFrom="opacity-0 scale-95"
							enterTo="opacity-100 scale-100"
							leave="ease-in duration-200"
							leaveFrom="opacity-100 scale-100"
							leaveTo="opacity-0 scale-95"
						>
							<Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all">
								{/* Header */}
								<div className="relative bg-gradient-to-r from-primary to-primary/90 px-6 py-8 text-white">
									<button
										className="absolute right-6 top-6 rounded-full p-1 text-white/80 transition-colors hover:bg-white/20 hover:text-white"
										onClick={onClose}
										aria-label="Zamknij modal"
									>
										<HiXMark size={24} />
									</button>
									
									<div className="flex items-start gap-6">
										<div className="relative h-24 w-24 flex-shrink-0 overflow-hidden rounded-full">
											<Image
												src={getAssetPath(img.path)}
												alt={img.description}
												width={96}
												height={96}
												className="h-full w-full object-cover"
											/>
										</div>
										<div className="flex-1">
											<Dialog.Title as="h3" className="text-2xl font-bold">
												{fullName}
											</Dialog.Title>
											<p className="mt-1 text-lg text-white/90">{position.label}</p>
											{videoUrl && (
												<a
													href={videoUrl}
													target="_blank"
													rel="noopener noreferrer"
													className="mt-3 inline-flex items-center gap-2 rounded-lg bg-white/20 px-4 py-2 text-sm font-medium transition-colors hover:bg-white/30"
												>
													<HiVideoCamera size={18} />
													Obejrzyj wideo
													<HiOutlinePlay size={16} />
												</a>
											)}
										</div>
									</div>
								</div>

								{/* Content */}
								<div className="max-h-[60vh] overflow-y-auto px-6 py-8">
									<div className="prose prose-gray max-w-none">
										<h4 className="mb-4 text-lg font-semibold text-gray-900">Opinia:</h4>
										<div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
											{reviewText}
										</div>
									</div>
								</div>

								{/* Footer */}
								<div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
									<div className="flex justify-end">
										<button
											onClick={onClose}
											className="rounded-lg bg-primary px-6 py-2 text-sm font-medium text-white transition-colors hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
										>
											Zamknij
										</button>
									</div>
								</div>
							</Dialog.Panel>
						</Transition.Child>
					</div>
				</div>
			</Dialog>
		</Transition>
	)
}
