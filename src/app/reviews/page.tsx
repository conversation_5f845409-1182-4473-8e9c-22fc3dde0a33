import { getReviewsPage } from '@/utils/api'
import { Metadata } from 'next'
import { Review } from './Review'

export async function generateMetadata(): Promise<Metadata> {
	const {
		pageData: { title, metaDescription: description }
	} = await getReviewsPage()
	return { title, description }
}

export default async function Reviews() {
	const { pageData, reviews } = await getReviewsPage()
	return (
		<section className="container mx-auto flex min-h-screen flex-col gap-8 px-4 pb-12 lg:px-12">
			<h1 className="title h-fit w-full text-gray-800">{pageData.title}</h1>
			<h2 className="font-rubik text-2xl font-semibold text-primary sm:text-3xl">
				{pageData.studentsSubtitle}
			</h2>
			<div className="flex h-full flex-wrap gap-8">
				{reviews
					.filter((review) => review.position.type === 'student')
					.map((review, index) => (
						<Review key={index} review={review} />
					))}
			</div>
			<h2 className="font-rubik text-2xl font-semibold text-primary sm:text-3xl">
				{pageData.parentsSubtitle}
			</h2>
			<div className="flex h-full flex-wrap gap-8">
				{reviews
					.filter((review) => review.position.type === 'parent')
					.map((review, index) => (
						<Review key={index} review={review} />
					))}
			</div>
		</section>
	)
}
