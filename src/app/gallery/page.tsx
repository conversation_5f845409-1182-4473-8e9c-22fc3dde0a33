import { getGalleryPage } from '@/utils/api'
import { getAssetPath } from '@/utils/assets'
import { Metadata } from 'next'
import Image from 'next/image'

export async function generateMetadata(): Promise<Metadata> {
	const { title } = await getGalleryPage()
	return { title }
}

export default async function Page() {
	const { title, images } = await getGalleryPage()
	return (
		<section className="container mx-auto flex min-h-screen flex-col gap-8 px-4 pb-8 lg:px-12">
			<h1 className="title w-full text-gray-800">{title}</h1>
			<div className="columns-1 sm:columns-2 lg:columns-3 xl:columns-4">
				{images.map(({ img }, index) => (
					<div
						className="group mb-6 grid w-full break-inside-avoid-column gap-6 overflow-hidden rounded-lg"
						key={index}
					>
						<Image
							src={getAssetPath(img.path)}
							alt={img.description}
							className="h-full w-full object-cover transition duration-300 ease-out group-hover:scale-105"
							width={500}
							height={(500 / img.width) * img.height}
						/>
					</div>
				))}
			</div>
			\
		</section>
	)
}
