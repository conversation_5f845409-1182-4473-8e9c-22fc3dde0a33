import { getFAQPage } from '@/utils/api'
import { Metadata } from 'next'
import { Answer } from './Answer'

export async function generateMetadata(): Promise<Metadata> {
	const data = await getFAQPage()
	return { title: data.title }
}

export default async function FAQ() {
	const data = await getFAQPage()
	return (
		<section className="relative min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 pt-8">
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.1),transparent_50%),radial-gradient(circle_at_80%_80%,rgba(255,119,198,0.1),transparent_50%)]"></div>
			<div className="container relative mx-auto px-4 py-20 lg:px-12">
				<div className="mx-auto max-w-4xl text-center">
					<div className="mb-6 inline-flex items-center rounded-full bg-gradient-to-r from-primary/10 to-secondary/10 px-6 py-2 text-sm font-semibold text-primary">
						<span className="mr-2 h-2 w-2 rounded-full bg-primary"></span>
						Najczęściej zadawane pytania
					</div>
					<h1 className="title mb-6 bg-gradient-to-r from-primary via-gray-900 to-secondary bg-clip-text text-transparent">{data.title}</h1>
					<p className="mx-auto mb-16 max-w-2xl text-lg leading-relaxed text-gray-600">
						Znajdź odpowiedzi na najważniejsze pytania dotyczące naszej szkoły, programu nauczania i procesu rekrutacji.
					</p>
				</div>
				<div className="mx-auto max-w-4xl">
					<div className="rounded-2xl bg-white/80 p-8 shadow-xl backdrop-blur-sm">
						<div className="flex flex-col divide-y divide-gray-200/50">
							{data.questions.map((item, index) => (
								<Answer key={index} question={item.question} answer={item.answer} />
							))}
						</div>
					</div>
				</div>
			</div>
		</section>
	)
}
