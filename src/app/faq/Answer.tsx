'use client'
import { useState } from 'react'
import { cn } from '@/utils/style'
import htmr from 'htmr'
import { HiChevronRight } from 'react-icons/hi2'

export const Answer = ({ question, answer }: { question: string; answer: string }) => {
	const [isOpen, setIsOpen] = useState(false)
	return (
		<div className={cn('group focus-config rounded-xl transition-all duration-300')}>
			<button
				className="flex w-full items-center justify-between gap-6 rounded-xl px-6 py-6 text-left transition-all duration-300 hover:bg-gradient-to-r hover:from-primary/5 hover:to-secondary/5"
				onClick={() => setIsOpen((prev) => !prev)}
			>
				<span className="font-semibold max-w-prose text-gray-800 transition-colors duration-300 group-hover:text-primary lg:text-lg">
					{question}
				</span>
				<div className="flex h-8 max-w-[2rem] w-full items-center justify-center rounded-full bg-gradient-to-r from-primary/10 to-secondary/10 transition-all duration-300 group-hover:from-primary group-hover:to-secondary group-hover:text-white">
					<HiChevronRight
						className={cn(
							'h-4 w-4 transform transition-all duration-300',
							isOpen && 'rotate-90'
						)}
					/>
				</div>
			</button>
			<div
				className={cn(
					'grid transition-all duration-300 ease-in-out',
					isOpen ? 'grid-rows-[1fr]' : 'grid-rows-[0fr]'
				)}
			>
				<div className={cn('overflow-hidden')}>
					<div className="content rounded-xl bg-gradient-to-r from-gray-50/50 to-white/50 px-6 pb-6 pt-2 text-gray-700 backdrop-blur-sm">
						{htmr(answer)}
					</div>
				</div>
			</div>
		</div>
	)
}
