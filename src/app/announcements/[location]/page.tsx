import { getAnnouncementsPage, getCities, getAmountOfPosts } from '@/utils/api'
import { Metadata } from 'next'
import Link from 'next/link'
import { cn } from '@/utils/style'
import Masonry from '@components/Masonry'
import { notFound } from 'next/navigation'

export async function generateMetadata({
	params: { location }
}: {
	params: { location: string }
}): Promise<Metadata> {
	const { pageData } = await getAnnouncementsPage()
	const cities = await getCities()
	const title = pageData.title + ' - ' + cities.find((city) => city.city === location)?.label
	return { title }
}

export const dynamic = 'force-static'

export async function generateStaticParams() {
	const cities = await getCities()
	return cities.map((city) => ({ params: { location: city.city } }))
}

export default async function Announcements({
	params: { location },
	searchParams: { page }
}: {
	params: { location: string }
	searchParams: { page?: string }
}) {
	const { pageData, posts } = await getAnnouncementsPage(Number(page ?? 1))
	const amountOfPosts = await getAmountOfPosts('announcement')

	const cities = await getCities()

	if (!cities.find((c) => c.city === location)) return notFound()
	return (
		<section className="container mx-auto mb-10 grid gap-8 px-4 lg:px-12">
			<h1 className="title text-gray-800">{pageData.title}</h1>
			<ul className="inline-flex gap-8">
				<li>
					<Link
						href={`/announcements`}
						className={
							'mt-4 h-fit w-fit border-b border-secondary border-opacity-0 text-center font-medium text-primary transition-all duration-300 hover:border-opacity-100 hover:text-secondary'
						}
					>
						Wszystkie miasta
					</Link>
				</li>
				{cities.map((city) => (
					<li key={city.city}>
						<Link
							href={`/announcements/${city.city}`}
							className={cn(
								'mt-4 h-fit w-fit border-b border-secondary border-opacity-0 text-center font-medium text-primary transition-all duration-300 hover:border-opacity-100 hover:text-secondary',
								location === city.city && 'border-opacity-50 text-secondary'
							)}
						>
							{city.label}
						</Link>
					</li>
				))}
			</ul>
			{posts.filter((post) => post.location === location).length !== 0 ? (
				<>
					<Masonry array={posts.filter((post) => post.location === location)} />
					{posts.length < amountOfPosts && (
						<Link
							className="btn mx-auto"
							href={`/announcements?page=${Number(page ?? 1) + 1}`}
							scroll={false}
						>
							Zobacz więcej
						</Link>
					)}
				</>
			) : (
				<div className="flex h-[40vh] w-full flex-col items-center justify-center gap-4 text-center">
					<h2 className="text-2xl font-medium text-gray-700">{pageData.noPostsTitle}</h2>
					<p className="text-gray-600">{pageData.noPostsSubtitle}</p>
				</div>
			)}
		</section>
	)
}
