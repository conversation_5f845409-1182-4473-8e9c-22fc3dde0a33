import InfoModal from '@/app/components/InfoModal'
import { getPost, getCommonTexts, getCities, getAnnouncements, getInfoModal } from '@/utils/api'
import { getAssetPath } from '@/utils/assets'
import { i18n } from '@/utils/i18n'
import htmr from 'htmr'
import { Metadata } from 'next'
import Image from 'next/image'
import { notFound } from 'next/navigation'
import {
	FaFacebook,
	FaGithub,
	FaInstagram,
	FaLinkedinIn,
	FaMedium,
	FaTiktok,
	FaTwitter,
	FaYoutube
} from 'react-icons/fa'
import { HiMap, HiOutlineCalendarDays } from 'react-icons/hi2'

export async function generateMetadata({
	params: { slug: slugArray }
}: {
	params: { slug: string[] }
}): Promise<Metadata> {
	const slug = slugArray.join('/')
	const data = await getPost(slug)
	const locale = i18n.defaultLocale
	if (!data) return {}
	const { title, description, img, date, keywords } = data
	return {
		title,
		description,
		keywords,
		openGraph: {
			url: `https://www.technischools.com/announcements/${slug}`,
			type: 'article',
			title,
			description,
			publishedTime: date,
			images: [
				{
					url: getAssetPath(img.path)
				}
			],
			locale
		},
		twitter: {
			card: 'summary_large_image',
			title,
			description,
			images: [
				{
					url: getAssetPath(img.path)
				}
			]
		}
	}
}

export async function generateStaticParams() {
	const posts = await getAnnouncements()
	return posts.map((post) => ({
		params: {
			slug: post.slug
		}
	}))
}

export default async function Page({
	params: { slug: slugArray }
}: {
	params: { slug: string[] }
}) {
	const slug = slugArray.join('/')
	const [post, commonTexts, cities, infoModalData] = await Promise.all([
		getPost(slug),
		getCommonTexts(),
		getCities(),
		getInfoModal()
	])
	const { authorText, shareText } = commonTexts
	const locale = i18n.defaultLocale
	if (!post) return notFound()

	const getSocialIcon = (name: string) => {
		switch (name) {
			case 'facebook':
				return (
					<FaFacebook className="h-5 w-auto text-primary duration-300 group-hover:text-secondary" />
				)
			case 'youtube':
				return (
					<FaYoutube className="h-5 w-auto text-primary duration-300 group-hover:text-secondary" />
				)
			case 'linkedin':
				return (
					<FaLinkedinIn className="h-5 w-auto text-primary duration-300 group-hover:text-secondary" />
				)
			case 'instagram':
				return (
					<FaInstagram className="h-5 w-auto text-primary duration-300 group-hover:text-secondary" />
				)
			case 'twitter':
				return (
					<FaTwitter className="h-5 w-auto text-primary duration-300 group-hover:text-secondary" />
				)
			case 'github':
				return (
					<FaGithub className="h-5 w-auto text-primary duration-300 group-hover:text-secondary" />
				)
			case 'medium':
				return (
					<FaMedium className="h-5 w-auto text-primary duration-300 group-hover:text-secondary" />
				)
			case 'tiktok':
				return (
					<FaTiktok className="h-5 w-auto text-primary duration-300 group-hover:text-secondary" />
				)
			default:
				return null
		}
	}
	const socialsShare = [
		{
			type: 'facebook',
			url:
				'https://www.facebook.com/sharer/sharer.php?u=' +
				`${process.env.NEXT_PUBLIC_WEBSITE_URL}/blog/${slug}`
		},
		{
			type: 'twitter',
			url:
				'https://twitter.com/intent/tweet?url=' +
				`${process.env.NEXT_PUBLIC_WEBSITE_URL}/blog/${slug}`
		}
	]
	return (
		<section className="container mx-auto mb-8 flex min-h-screen flex-col gap-8 px-4 lg:px-12">
			<h1 className="title w-full text-gray-800">{post.title}</h1>
			<hr className="border-gray-300" />
			<div className="flex items-center justify-between gap-6 max-sm:flex-col">
				<div className="flex items-center gap-8 max-sm:flex-col">
					<div className="flex items-center gap-2 text-sm text-gray-600 duration-300 hover:text-secondary">
						<HiOutlineCalendarDays size={16} />
						<span>
							{new Date(post.date).toLocaleDateString(locale, {
								year: 'numeric',
								month: 'long',
								day: 'numeric'
							})}
						</span>
					</div>
					<div className="flex items-center gap-2 text-sm text-gray-600 duration-300 hover:text-secondary">
						<HiMap size={16} />
						<span>{cities.find((city) => city.city === post.location)?.label}</span>
					</div>
					{post.author && (
						<div className="group flex items-center gap-2 text-sm text-gray-600 duration-300 hover:text-secondary">
							<span>{authorText}</span>
							{post.authorSocial ? (
								<a
									href={post.authorSocial}
									target="_blank"
									aria-label={post.author + ' social link'}
									className="text-gray-800 duration-300 group-hover:text-secondary"
								>
									{post.author}
								</a>
							) : (
								<span className="text-gray-800 duration-300 group-hover:text-secondary">
									{post.author}
								</span>
							)}
						</div>
					)}
				</div>
				<div className="flex items-center gap-4">
					<span className="text-sm text-gray-600">{shareText}</span>
					<div className="flex gap-4">
						{socialsShare.map((item, index) => (
							<a
								key={index}
								href={item.url}
								target="_blank"
								rel="noreferrer"
								className="group rounded-full bg-gray-50 p-3 duration-300 ease-out hover:scale-110"
							>
								{getSocialIcon(item.type)}
							</a>
						))}
					</div>
				</div>
			</div>
			<hr className="border-gray-300" />
			<div className="grid">
				<Image
					alt="image"
					src={getAssetPath(post.img.path)}
					width={post.img.width}
					height={post.img.height}
					className="mx-auto w-full max-w-3xl rounded-xl"
				/>
				<div className="content mx-auto max-w-3xl [&>p:first-of-type]:mt-8 [&>p]:leading-6">
					{htmr(post.content, {
						transform: {
							img: (node) => (
								// eslint-disable-next-line @next/next/no-img-element
								<img
									alt={node.alt}
									src={
										node.src?.includes('storage/uploads')
											? `${process.env.NEXT_PUBLIC_ASSETS_URL}${node.src.split('/uploads')[1]}`
											: node.src
									}
								/>
							)
						}
					})}
				</div>
			</div>
			{!post.disableInfoModal && <InfoModal data={infoModalData} />}
		</section>
	)
}
