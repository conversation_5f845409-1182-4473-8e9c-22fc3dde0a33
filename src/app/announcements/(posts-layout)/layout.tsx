import { getAnnouncementsLayout, getCities } from '@/utils/api'
import { Metadata } from 'next'
import Link from 'next/link'

export async function generateMetadata(): Promise<Metadata> {
	const { title } = await getAnnouncementsLayout()
	return { title }
}

export default async function AnnouncementsLayout({ children }: { children: React.ReactNode }) {
	const { title } = await getAnnouncementsLayout()
	const cities = await getCities()

	return (
		<section className="container mx-auto mb-10 grid gap-8 px-4 lg:px-12">
			<h1 className="title text-gray-800">{title}</h1>
			<ul className="inline-flex gap-8">
				<li>
					<Link
						href={`/announcements`}
						className={
							'mt-4 h-fit w-fit border-b border-secondary border-opacity-50 text-center font-medium text-secondary transition-all duration-300 hover:border-opacity-100 '
						}
					>
						Wszystkie miasta
					</Link>
				</li>
				{cities.map((city) => (
					<li key={city.city}>
						<Link
							href={`/announcements/${city.city}`}
							className="mt-4 h-fit w-fit border-b border-secondary border-opacity-0 text-center font-medium text-primary transition-all duration-300 hover:border-opacity-100 hover:text-secondary"
						>
							{city.label}
						</Link>
					</li>
				))}
			</ul>
			{children}
		</section>
	)
}
