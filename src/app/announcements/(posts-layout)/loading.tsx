export default async function AnnouncementsPlaceholder() {
	return (
		<div className="columns-1 gap-6 md:columns-2 lg:columns-3 xl:columns-4">
			{Array.from({ length: 10 }).map((_, i) => (
				<>
					<div
						key={i.toString() + 'short'}
						className="mb-6 h-64 w-full animate-pulse break-inside-avoid-column rounded-lg bg-gray-200"
					/>
					<div
						key={i.toString() + 'long'}
						className="mb-6 h-96 w-full animate-pulse break-inside-avoid-column rounded-lg bg-gray-200"
					/>
				</>
			))}
		</div>
	)
}
