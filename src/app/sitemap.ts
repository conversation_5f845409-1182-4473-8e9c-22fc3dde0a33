import { getAnnouncements, getArticles, getCities, getCustomPagesInfo } from '@/utils/api'
import { STAFF_TYPES } from '@/utils/constants'
import fs from 'fs'
import type { MetadataRoute } from 'next'

const appFolders = fs.readdirSync('./src/app', { withFileTypes: true })
const folderPages = appFolders
	.filter(
		(file) =>
			file.isDirectory() &&
			!file.name.startsWith('(') &&
			!file.name.startsWith('[') &&
			!['api', 'components'].includes(file.name)
	)
	.map((folder) => folder.name)

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
	const [articles, announcements, customPages, cities] = await Promise.all([
		getArticles(),
		getAnnouncements(),
		getCustomPagesInfo(),
		getCities()
	])
	const mainPage = {
		url: process.env.NEXT_PUBLIC_WEBSITE_URL ?? 'https://technischools.com',
		lastModified: new Date(),
		priority: 1
	}
	const folderPagesUrls = folderPages.map((page) => ({
		url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/${page}`,
		lastModified: new Date()
	}))

	const articleUrls = articles.map((post) => ({
		url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/blog/${post.slug}`,
		lastModified: new Date(post.date)
	}))
	const announcementUrls = announcements.map((post) => ({
		url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/announcements/${post.location}/${post.slug}`,
		lastModified: new Date(post.date)
	}))
	const customPageUrls = customPages.map((page) => ({
		url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/${page.slug}`,
		lastModified: new Date(page._modified * 1000)
	}))

	const announcementLocationsUrls = cities.map((city) => ({
		url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/announcements/${city.city}`,
		lastModified: new Date()
	}))
	const staffTypeUrls = STAFF_TYPES.map((type) => ({
		url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/staff/${type}`,
		lastModified: new Date()
	}))
	const staffLocationsUrls = cities.map((city) => ({
		url: `${process.env.NEXT_PUBLIC_WEBSITE_URL}/staff/teachers/${city.city}`,
		lastModified: new Date()
	}))

	return [
		mainPage,
		...articleUrls,
		...announcementUrls,
		...customPageUrls,
		...folderPagesUrls,
		...announcementLocationsUrls,
		...staffTypeUrls,
		...staffLocationsUrls
	]
}
