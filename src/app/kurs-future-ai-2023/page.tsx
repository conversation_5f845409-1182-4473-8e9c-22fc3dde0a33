import { cn } from '@/utils/style'
import Image from 'next/image'
import { HiArrowRight, HiEnvelope, HiPhone } from 'react-icons/hi2'
import TypeformTracker from '../components/TypeformTracker'

export const metadata = {
	title: 'KURS AI & CYBERSECURITY',
	description:
		'8-klasisto! Weź udział w kursie i zdobądź wiedzę ze sztucznej inteligencji i cyberbezpieczeństwa!',
	keywords: [
		'KURS AI & CYBERSECURITY',
		'8 klasa',
		'kurs',
		'szkoła',
		'sztuczna inteligencja',
		'cyberbezpieczeństwo',
		'wiedza',
		'edukacja'
	]
}

const SPEAKERS = [
	{
		fullName: '<PERSON><PERSON><PERSON>',
		bio: 'Doświadczony programista i etyczny hacker, który nie tylko posiada zaawansowane umiejętności techniczne. Jako założyciel Techni Schools, Mateusz skupia się na edukacji przyszłych talentów programistycznych. Partner w funduszu inwestycyjnym Techni Ventures, w którym aktywnie inwestuje w obiecujące startupy. Poza życiem zawodowym, Mateusz jest również lokalnym patriotą co podkreśla jego zaangażowanie w budowanie silnej społeczności i przyszłości dla regionu.',
		img: {
			path: 'https://dev.technischools.com/assets/link/652ce7ec53f52647cc06f391',
			alt: 'Mateusz Kozłowski'
		}
	},

	{
		fullName: 'Paweł Nowak',
		bio: 'Zawodowo programista w projektach komercyjnych opartych na AI, a także nauczyciel przedmiotów ścisłych. Absolwent UMCS. Doświadczenie zawodowe zdobył w licznych projektach B+R lub komercyjnych, gdzie wykorzystywał takie techniki jak np. analiza ludzkiego głosu, przetwarzanie języka naturalnego, prognozowanie w szeregach czasowych.',
		img: {
			path: 'https://dev.technischools.com/assets/link/64477cf251476ba2a709c8cf',
			alt: 'Paweł Nowak'
		}
	},
	{
		fullName: 'Kuba Sawulski',
		bio: 'Programuje od 13 roku życia, ukończył Politechnikę Warszawską na kierunku Automatyka i Robotyka, zdobywając Stypendium Ministra Nauki za wybitne osiągnięcia. Obecnie rozwija własną usługę dostarczającą skalowalne API do generowania kodów QR, wykorzystując Python, TypeScript oraz technologie chmurowe takie jak AWS i GCP.',
		img: {
			path: 'https://dev.technischools.com/assets/link/654b7f1be5e08a63da0999c1',
			alt: 'Kuba Sawulski'
		}
	}
]

export default function LubelskaWyspaPage() {
	return (
		<>
			<div className="bg-indigo-950">
				<Section className="flex h-full items-center justify-between md:flex-row md:pb-24">
					<div>
						<hgroup className="space-y-6 max-md:text-center">
							<h1 className="font-rubik text-4xl font-semibold text-white [text-shadow:_0_1.5px_0_rgb(0_0_0_/_20%)] [text-wrap:balance] md:text-7xl">
								KURS AI & CYBERSECURITY
							</h1>
							<p className="text-indigo-200 [text-wrap:balance] md:text-lg">
								8-klasisto! Weź udział w kursie i zdobądź wiedzę ze sztucznej inteligencji i
								cyberbezpieczeństwa!
							</p>
						</hgroup>
						<a
							href="#signup"
							className="btn-lg group mt-8 flex rounded-xl border-none bg-gradient-to-b from-[#f23da1] to-[#f20487] py-3 text-white shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] [text-shadow:_0_1px_0_rgb(0_0_0_/_20%)] hover:brightness-105 active:scale-[0.98] max-md:mx-auto sm:text-lg"
						>
							Zapisz się
							<HiArrowRight className="ml-2 h-4 w-4 transition-transform [stroke-width:2px] group-hover:translate-x-1" />
						</a>
					</div>
					<div className="relative h-full overflow-hidden rounded-full max-sm:hidden">
						<Image
							src={'https://dev.technischools.com/assets/link/654b6ac0fa9592884508f970'}
							alt="Lubelska WySPA Techni"
							width={400}
							height={400}
							objectFit="cover"
						/>
					</div>
				</Section>
			</div>
			<Section>
				<SectionTitle>Wydarzenie</SectionTitle>
				<div className="max-w-prose space-y-5 font-normal text-zinc-700 sm:text-lg">
					<p>
						<span className="font-rubik font-medium text-zinc-950">DARMOWY</span> kurs{' '}
						<span className="font-rubik font-medium text-zinc-950">Techni AI & Cybersecurity</span>{' '}
						to 10 stacjonarnych i praktycznych zajęć prowadzonych przez aktywnych{' '}
						<span className="font-rubik font-medium text-zinc-950">zawodowo programistów</span>.
						Kurs jest skierowany dla{' '}
						<span className="font-rubik font-medium text-zinc-950">uczniów klas 8</span> chcących
						lepiej poznać świat programowania i sztucznej inteligencji.
					</p>
					<p>
						<span className="font-rubik font-medium text-zinc-950">Założenia kursu:</span>
						<ul className="list-disc space-y-2 pl-5">
							<li>10 spotkań</li>
							<li>raz w tygodniu</li>
							<li>spotkania po 90 min</li>
							<li>kurs jest bezpłatny</li>
							<li>po ukończeniu kursu otrzymasz certyfikat</li>
							<li>przychodzisz ze swoim laptopem lub robisz notatki i pracujesz w domu</li>
						</ul>
					</p>
					<p>
						Kurs jest najlepszym pomysłem, jeśli zastanawiasz się czy rozpocząć swoją karierę jako
						programista i chcesz zapisać się do technikum programistycznego.
					</p>
					<p>
						Zainteresowany?{' '}
						<a
							href="#signup"
							className="font-rubik font-medium text-primary underline underline-offset-2"
						>
							Zapisz się już dziś!
						</a>
					</p>
				</div>
			</Section>

			<div className="bg-stepper bg-cover bg-top bg-no-repeat">
				<Section>
					<SectionTitle>Agenda</SectionTitle>
					<div className="mt-2 h-full rounded-2xl border bg-white p-6 shadow-md ring-1 ring-black/5">
						<div className="grid h-fit w-fit grid-cols-[10rem_1fr] gap-y-4 sm:text-lg">
							<span className="mr-2 font-rubik font-semibold tabular-nums">Spotkania 1-3:</span>
							<span>Programowanie w pythonie</span>
							<span className="mr-2 font-rubik font-semibold tabular-nums">Spotkania 4-6:</span>
							<span>Tworzenie sztucznej inteligencji</span>
							<span className="mr-2 font-rubik font-semibold tabular-nums">Spotkania 7:</span>
							<span>Przewidywanie cen kryptowalut w przyszłości</span>
							<span className="mr-2 font-rubik font-semibold tabular-nums">Spotkania 8-9:</span>
							<span>
								Nauka kontroli dostępu -  czyli dlaczego zdarza się, że dane trafiają w niepowołane
								ręce & Łamanie stron internetowych i haseł
							</span>

							<span className="mr-2 font-rubik font-semibold tabular-nums">Spotkanie 10:</span>

							<span>Biały wywiad</span>
						</div>
					</div>
				</Section>
			</div>
			<Section>
				<SectionTitle>Prelegenci</SectionTitle>
				<div className="flex h-full flex-wrap gap-8">
					{SPEAKERS.map((prelegent) => (
						<div className="h-fit w-full max-w-xl rounded-xl p-4 shadow-md ring-1 ring-black/5 md:h-96">
							<Image
								src={prelegent.img.path}
								alt={prelegent.img.alt}
								width={160}
								height={160}
								className="aspect-square rounded-md object-cover object-top shadow-md ring-1 ring-black/5 max-sm:mb-4 max-sm:w-full sm:float-left sm:mr-4"
							/>
							<h4 className="mb-2 font-rubik text-xl font-medium">{prelegent.fullName}</h4>
							<div className="space-y-3 leading-8">
								{prelegent.bio.split('\n').map((paragraph, i) => (
									<p key={i} className="text-zinc-700">
										{paragraph}
									</p>
								))}
							</div>
						</div>
					))}
				</div>
			</Section>
			<div className="bg-stepper bg-cover bg-no-repeat">
				<Section id="signup">
					<SectionTitle>Zapisz się!</SectionTitle>

					<div
						data-tf-widget="oFtAazyP"
						data-tf-opacity="100"
						data-tf-iframe-props="title=Techni Schools Rekrutacja"
						data-tf-transitive-search-params
						data-tf-medium="snippet"
						data-tf-hidden="proces="
						className="mx-auto h-[700px] w-[800px] max-w-full rounded-xl bg-white p-3 shadow-md ring-1 ring-black/5"
					/>
					<TypeformTracker formName="kurs-future-ai-2023" />
				</Section>
			</div>
			<div className="w-full bg-gradient-to-b from-indigo-900 to-indigo-950 shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)]">
				<Section id="signup">
					<SectionTitle hideRectangle>Data i miejsce</SectionTitle>
					<div className="flex items-center justify-around gap-8 text-white max-sm:flex-col">
						<div className="flex flex-col items-center justify-center">
							<div className="relative mb-8 aspect-square w-48 overflow-hidden rounded-2xl md:w-72">
								<Image
									alt="warsaw building"
									src="https://dev.technischools.com/assets/link/654b7bcff2238c923e0eb7f0"
									layout="fill"
								/>
							</div>
							<h2 className="mb-4 font-rubik text-3xl font-medium">WARSZAWA</h2>
							<p className="mb-2">5 grudnia 2023</p>
							<p className="mb-2">ul. Okopowa 59 (V piętro)</p>
							<p className="mb-2">wtorki, godzina 17:00</p>
						</div>

						<div className="flex flex-col items-center justify-center">
							<div className="relative mb-8 aspect-square w-48 overflow-hidden rounded-2xl md:w-72">
								<Image
									alt="lublin building"
									src="https://dev.technischools.com/assets/link/654b7bcff2238c923e0eb7f1"
									layout="fill"
									objectFit="cover"
								/>
							</div>
							<h2 className="mb-4 font-rubik text-3xl font-medium">LUBLIN</h2>
							<p className="mb-2">7 grudnia 2023</p>
							<p className="mb-2">ul. Narutowicza 55b (II piętro)</p>
							<p className="mb-2">czwartki, godzina 17:30</p>
						</div>
					</div>
				</Section>
			</div>
			<Section>
				<SectionTitle>Kontakt</SectionTitle>
				<div className="flex items-center justify-between gap-8 max-xl:flex-col">
					<div className="flex w-full flex-col items-center justify-center gap-x-8 gap-y-2 md:flex-row">
						<span className="font-rubik text-lg font-medium text-zinc-950">Zadzwon do nas:</span>

						<a
							href="tel:+48721221299"
							className="btn-lg group flex rounded-xl border-none bg-gradient-to-b from-indigo-900 to-indigo-950 py-3 text-white shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] [text-shadow:_0_1px_0_rgb(0_0_0_/_20%)] hover:brightness-105 active:scale-[0.98] max-md:mx-auto max-md:w-full sm:text-lg"
							aria-label="{`Call ${data.phone}`}"
						>
							<span className="mr-1 grid h-5 w-5 place-items-center">
								<HiPhone className="min-w-full" />
							</span>
							+48 721 221 299
						</a>
					</div>
					<div className="flex w-full flex-col items-center justify-center gap-x-8 gap-y-2 md:flex-row">
						<span className="font-rubik text-lg font-medium text-zinc-950">lub napisz:</span>
						<a
							href="mailto:<EMAIL>"
							className="btn-lg group flex rounded-xl border-none bg-gradient-to-b from-indigo-900 to-indigo-950 py-3 text-white shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] [text-shadow:_0_1px_0_rgb(0_0_0_/_20%)] hover:brightness-105 active:scale-[0.98] max-md:mx-auto max-md:w-full sm:text-lg"
							aria-label="Email <EMAIL>"
						>
							<span className="mr-1 grid h-5 w-5 place-items-center">
								<HiEnvelope className="min-w-full" />
							</span>
							<EMAIL>
						</a>
					</div>
					<div className="flex w-full flex-col items-center justify-center gap-x-8 gap-y-2 md:flex-row">
						<span className="font-rubik text-lg font-medium text-zinc-950">Zobacz też:</span>
						<a
							href="https://technischools.com/regulamin-ai-cybersec-23-24"
							className="btn-lg group flex rounded-xl border-none bg-gradient-to-b from-indigo-900 to-indigo-950 py-3 text-white shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] [text-shadow:_0_1px_0_rgb(0_0_0_/_20%)] hover:brightness-105 active:scale-[0.98] max-md:mx-auto max-md:w-full sm:text-lg"
							aria-label="Email <EMAIL>"
						>
							Regulamin
						</a>
					</div>
				</div>
			</Section>
		</>
	)
}

function Section({
	children,
	className,

	...props
}: React.HTMLAttributes<HTMLDivElement> & {
	children: React.ReactNode
	className?: string
}) {
	return (
		<section className={cn('container mx-auto px-4 py-12 lg:px-12', className)} {...props}>
			{children}
		</section>
	)
}

function SectionTitle({
	children,
	className,
	hideRectangle = false,
	...props
}: React.HTMLAttributes<HTMLDivElement> & {
	children: React.ReactNode
	className?: string
	hideRectangle?: boolean
}) {
	return (
		<div
			className={cn('relative mb-8 flex h-20 w-full items-center text-white', className)}
			{...props}
		>
			{!hideRectangle && (
				<div className="absolute -left-1/2 top-0 h-full w-full rounded-3xl bg-gradient-to-b from-indigo-900 to-indigo-950 shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] sm:-left-2/3"></div>
			)}
			<h2 className="relative z-[2] font-rubik text-2xl font-bold [text-shadow:_0_1.5px_0_rgb(0_0_0_/_20%)] sm:text-3xl lg:text-5xl">
				{children}
			</h2>
		</div>
	)
}
