'use client'
import { CustomPageSchema } from '@/app/components/custom-page-components'
import { CustomPageSchemaReducerAction } from '@/app/components/custom-page-components/reducer'
import { MutationResponse, SchemaSaveResponse } from '@/types/page-builder-ai'
import { useState } from 'react'
import { applyPatch } from 'fast-json-patch'
import { useLocalStorage } from '@/utils/hooks'

interface AIAssistantProps {
	schema: CustomPageSchema
	dispatch: React.Dispatch<CustomPageSchemaReducerAction>
	onLoadingChange?: (isLoading: boolean) => void
}
const emptySchema = {
	constants: {
		background: '#ffffff',
		accentColors: ['#000000', '#050505'],
		mutedTextColor: '#71717a',
		textColor: '#000000',
		mutedBackground: '#e4e4e7',
		backgroundRing: '#e4e4e7',
		displayFontFamily: 'Inter, sans-serif',
		defaultFontFamily: 'Inter, sans-serif'
	},
	sections: []
}

export function AIAssistant({ schema, dispatch, onLoadingChange }: AIAssistantProps) {
	const [prompt, setPrompt] = useState('')
	const [lastPrompt, setLastPrompt] = useState('')
	const [isLoading, setIsLoading] = useState(false)
	const [controller, setController] = useState<AbortController | null>(null)
	const [lastSchemaId, setLastSchemaId] = useLocalStorage<string | null>('lastSchemaId', null)
	const [lastSchemaState, setLastSchemaState] = useLocalStorage<string | null>(
		'lastSchemaState',
		null
	)

	const hasContent = schema.sections.length > 0

	const [error, setError] = useState<string | null>(null)

	const handleSubmit = async (promptToUse = prompt) => {
		setError(null)
		if (!promptToUse) return

		const abortController = new AbortController()
		setController(abortController)
		setIsLoading(true)
		onLoadingChange?.(true)
		setLastPrompt(promptToUse)

		if (promptToUse === lastPrompt) {
			setPrompt(promptToUse)
		}

		try {
			if (!hasContent) {
				const response = await fetch('/api/page-builder/ai/create', {
					method: 'POST',
					body: JSON.stringify({ prompt: promptToUse }),
					signal: abortController.signal
				})

				if (!response.ok) {
					if (response.status === 401) {
						setError('Unauthorized. Please provide a valid PB AI Token.')
					} else if (response.status === 403) {
						setError('Access denied. You do not have permission to use this feature.')
					} else if (response.status === 429) {
						setError('Too many requests. Please wait a moment before trying again.')
					} else if (response.status >= 500) {
						setError('Server error. Please try again later.')
					} else {
						setError(`Request failed with status: ${response.status}`)
					}
					setIsLoading(false)
					onLoadingChange?.(false)
					setPrompt('')
					setController(null)
					return
				}

				const responseData = (await response.json()) as MutationResponse
				const newSchema = applyPatch(
					JSON.parse(JSON.stringify(emptySchema)),
					responseData.operations
				).newDocument
				dispatch({ type: 'SET_SCHEMA', payload: newSchema })
			} else {
				const currentSchemaState = JSON.stringify(schema)
				const isSchemaModified = currentSchemaState !== lastSchemaState

				let schemaId = lastSchemaId
				if (isSchemaModified) {
					const schemaResponse = await fetch('/api/page-builder/ai/schema', {
						method: 'POST',
						body: JSON.stringify({
							schema,
							schemaId: lastSchemaId || undefined
						}),
						signal: abortController.signal
					})
					const data = (await schemaResponse.json()) as SchemaSaveResponse
					schemaId = data.schemaId
					setLastSchemaId(schemaId)
					setLastSchemaState(currentSchemaState)
				}

				const response = await fetch('/api/page-builder/ai/continue', {
					method: 'POST',
					body: JSON.stringify({ prompt: promptToUse, schemaId }),
					signal: abortController.signal
				})

				if (!response.ok) {
					if (response.status === 401) {
						setError('Unauthorized. Please sign in to use the AI assistant.')
					} else if (response.status === 403) {
						setError('Access denied. You do not have permission to use this feature.')
					} else if (response.status === 429) {
						setError('Too many requests. Please wait a moment before trying again.')
					} else if (response.status >= 500) {
						setError('Server error. Please try again later.')
					} else {
						setError(`Request failed with status: ${response.status}`)
					}
					setIsLoading(false)
					onLoadingChange?.(false)
					setPrompt('')
					setController(null)
					return
				}

				const responseData = (await response.json()) as MutationResponse
				dispatch({ type: 'APPLY_OPERATIONS', payload: responseData.operations })
			}
		} catch (error: any) {
			if (error.name === 'AbortError') {
				console.log('Request cancelled')
			} else {
				console.error('Error:', error)
				setError('An error occurred. Please try again.')
			}
		}

		setIsLoading(false)
		onLoadingChange?.(false)
		setPrompt('')
		setController(null)
	}

	const handleStop = () => {
		controller?.abort()
		setIsLoading(false)
		onLoadingChange?.(false)
		setController(null)
	}

	return (
		<div className="flex flex-col gap-6 rounded-lg border border-zinc-200 p-6">
			<h3 className="m-0 text-lg font-medium">AI Assistant</h3>

			{error && (
				<div className="rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-600">
					{error}
				</div>
			)}

			{lastPrompt && !isLoading && (
				<div className="flex items-center gap-4 rounded-lg border border-zinc-200 bg-zinc-50 p-4">
					<div className="flex-1">
						<div className="mb-1 text-xs font-medium text-zinc-500">Last prompt</div>
						<div className="text-sm text-zinc-900">{lastPrompt}</div>
					</div>
					<button
						onClick={() => handleSubmit(lastPrompt)}
						className="shrink-0 rounded-md border border-zinc-300 px-3 py-1.5 text-sm font-medium text-zinc-900 transition-colors hover:bg-zinc-100"
					>
						Run again
					</button>
				</div>
			)}

			<div className="relative">
				<textarea
					value={prompt}
					onChange={(e) => setPrompt(e.target.value)}
					onKeyDown={(e) => {
						if (e.key === 'Enter' && !e.shiftKey) {
							e.preventDefault()
							handleSubmit()
						}
					}}
					placeholder={
						hasContent
							? 'Describe how you want to modify the page...'
							: 'Describe the page you want to create...'
					}
					className="min-h-[8rem] w-full rounded-lg border border-zinc-200 p-4 pr-24 text-sm shadow-sm transition-colors focus:border-zinc-400 focus:outline-none disabled:opacity-50"
					disabled={isLoading}
				/>
				{isLoading ? (
					<button
						onClick={handleStop}
						className="absolute bottom-4 right-4 rounded-md border border-red-200 px-4 py-2 text-sm font-medium text-red-600 transition-colors hover:bg-red-50"
					>
						Stop
					</button>
				) : (
					<button
						onClick={() => handleSubmit()}
						disabled={!prompt}
						className="absolute bottom-4 right-4 rounded-md bg-zinc-900 px-4 py-2 text-sm font-medium text-white transition-all hover:bg-zinc-800 disabled:opacity-50"
					>
						Send
					</button>
				)}
			</div>
		</div>
	)
}
