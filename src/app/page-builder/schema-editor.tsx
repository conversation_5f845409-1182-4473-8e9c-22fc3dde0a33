'use client'
import { cn } from '@/utils/style'
import { capitalize } from '@/utils/text'
import { Dispatch, useState } from 'react'
import { AVAILABLE_SECTIONS, CustomPageSchema } from '../components/custom-page-components'
import { CustomPageSchemaReducerAction } from '../components/custom-page-components/reducer'
import { ConstantsForm } from './components/constants-form'
import { GenericSectionPropsForm } from './components/generic-section-props-form'
import { ReorderModal } from './components/reorder-modal'
import { SectionForm } from './components/section-form'
import { TitleForm } from './components/title-form'

type Props = {
	schema: CustomPageSchema
	dispatch: Dispatch<CustomPageSchemaReducerAction>
}
export function SchemaEditor({ schema, dispatch }: Props) {
	const [nextSection, setNextSection] = useState<(typeof AVAILABLE_SECTIONS)[number]>('hero')
	const [isReorderOpen, setIsReorderOpen] = useState(false)
	const [activeTab, setActiveTab] = useState<'constants' | 'sections'>('sections')
	const [collapsedSections, setCollapsedSections] = useState<string[]>([])

	const getSectionKey = (section: CustomPageSchema['sections'][number]) =>
		`${section.props.id}-${section.type}`

	const toggleSection = (section: CustomPageSchema['sections'][number]) => {
		const key = getSectionKey(section)
		setCollapsedSections((prev) =>
			prev.includes(key) ? prev.filter((k) => k !== key) : [...prev, key]
		)
	}
	return (
		<div className="flex flex-col gap-6 rounded-lg border border-zinc-200 p-6">
			<div className="flex items-center gap-4 border-b border-zinc-200">
				<button
					onClick={() => setActiveTab('sections')}
					className={cn(
						'pb-3 text-sm font-medium transition-colors',
						activeTab === 'sections'
							? 'border-b-2 border-zinc-900 text-zinc-900'
							: 'text-zinc-500 hover:text-zinc-900'
					)}
				>
					Sections
				</button>
				<button
					onClick={() => setActiveTab('constants')}
					className={cn(
						'pb-3 text-sm font-medium transition-colors',
						activeTab === 'constants'
							? 'border-b-2 border-zinc-900 text-zinc-900'
							: 'text-zinc-500 hover:text-zinc-900'
					)}
				>
					Constants
				</button>
			</div>
			{activeTab === 'sections' && (
				<div className="flex flex-col gap-4">
					<button
						onClick={() => setIsReorderOpen(true)}
						disabled={schema.sections.length < 2}
						className="inline-flex items-center gap-2 self-end rounded-md border border-zinc-200 px-3 py-2 text-sm font-medium text-zinc-600 transition-colors hover:border-zinc-300 hover:bg-zinc-50 hover:text-zinc-900 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:border-zinc-200 disabled:hover:bg-transparent"
					>
						Reorder
					</button>
					{schema.sections.map((section, index) => (
						<div
							key={getSectionKey(section)}
							className="flex flex-col gap-3 rounded-lg border border-zinc-200 p-4"
						>
							<div
								className="flex cursor-pointer items-center justify-between"
								onClick={() => toggleSection(section)}
							>
								<div className="flex items-center gap-2">
									<span className="select-none text-sm text-zinc-600">
										{collapsedSections.includes(getSectionKey(section)) ? '▸' : '▾'}
									</span>
									<span className="text-sm font-medium">{section.type.split('-').join(' ')}</span>
								</div>
								<div className="flex items-center gap-2">
									<button
										onClick={(e) => {
											e.stopPropagation()
											dispatch({ type: 'MOVE_SECTION_UP', payload: { index } })
										}}
										disabled={index === 0}
										className="h-8 w-8 rounded-md border border-zinc-200 p-1.5 text-sm transition-colors hover:bg-zinc-50 disabled:opacity-50"
										title="Move up"
									>
										↑
									</button>
									<button
										onClick={(e) => {
											e.stopPropagation()
											dispatch({ type: 'MOVE_SECTION_DOWN', payload: { index } })
										}}
										disabled={index === schema.sections.length - 1}
										className="h-8 w-8 rounded-md border border-zinc-200 p-1.5 text-sm transition-colors hover:bg-zinc-50 disabled:opacity-50"
										title="Move down"
									>
										↓
									</button>
									<button
										onClick={(e) => {
											e.stopPropagation()
											dispatch({ type: 'REMOVE_SECTION', payload: { index } })
										}}
										className="h-8 w-8 rounded-md border border-zinc-200 p-1.5 text-sm text-red-500 transition-colors hover:border-red-200 hover:bg-red-50"
										title="Remove"
									>
										×
									</button>
								</div>
							</div>

							{!collapsedSections.includes(getSectionKey(section)) && (
								<>
									{section.type !== 'hero' && (
										<>
											<SectionForm
												props={section.props}
												dispatch={dispatch}
												index={index}
												id={`section-id-${getSectionKey(section)}`}
											/>
											<div>
												<h4 className="mb-4 mt-8">Title:</h4>
												<TitleForm
													props={section.props?.titleProps!}
													dispatch={dispatch}
													index={index}
													id={`title-form-id-${getSectionKey(section)}`}
												/>
											</div>
										</>
									)}
									<div>
										{section.type !== 'hero' && <h4 className="mb-4 mt-8">Section specific:</h4>}
										<GenericSectionPropsForm
											section={section}
											dispatch={dispatch}
											index={index}
											id={`generic-id-${getSectionKey(section)}`}
										/>
									</div>
								</>
							)}
						</div>
					))}

					<ReorderModal
						onReorder={(newItems) =>
							dispatch({ type: 'REORDER_SECTIONS', payload: newItems.map((i) => i.value) })
						}
						items={schema.sections.map((section) => ({
							label: `${section.props.title}${section.props.title && ' -'} ${capitalize(
								section.type.split('-').join(' ')
							)}`,
							value: section
						}))}
						open={isReorderOpen}
						setOpen={setIsReorderOpen}
					/>
					<div className="mx-auto flex w-96 items-center justify-center gap-4">
						<select
							className="input"
							value={nextSection}
							onChange={(event) =>
								setNextSection(event.target.value as (typeof AVAILABLE_SECTIONS)[number])
							}
						>
							{AVAILABLE_SECTIONS.map((section) => (
								<option key={section} value={section}>
									{section}
								</option>
							))}
						</select>

						<button
							onClick={() =>
								dispatch({
									type: 'ADD_EMPTY_SECTION',
									section: nextSection
								})
							}
							className="whitespace-nowrap rounded-md border border-dashed border-zinc-200 px-4 py-3 text-sm font-medium text-zinc-500 transition-colors hover:border-zinc-300 hover:bg-zinc-50"
						>
							Add section
						</button>
					</div>
				</div>
			)}
			{activeTab === 'constants' && <ConstantsForm schema={schema} dispatch={dispatch} />}
		</div>
	)
}

// TODO make a reset button so smth resets to the constants near each field that has an onReset prop
// TODO add default values to the fields, not undefined, because sometimes the constant is being used but it shows undefined or random values (#ffff00)
