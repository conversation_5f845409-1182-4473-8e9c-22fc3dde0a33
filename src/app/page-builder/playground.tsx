'use client'
import { useAutosave, useLocalStorage } from '@/utils/hooks'
import { cn } from '@/utils/style'
import { useEffect, useState } from 'react'
import CustomPageBuilder, { CustomPageSchema } from '../components/custom-page-components'
import { useCustomPageSchemaReducer } from '../components/custom-page-components/reducer'
import { AIAssistant } from './ai-assistant'
import { SchemaEditor } from './schema-editor'
import './styles/forms.css'
const emptySchema = {
	constants: {
		background: '#ffffff',
		accentColors: ['#000000', '#050505'],
		mutedTextColor: '#71717a',
		textColor: '#000000',
		mutedBackground: '#e4e4e7',
		backgroundRing: '#e4e4e7',
		displayFontFamily: 'Inter, sans-serif',
		defaultFontFamily: 'Inter, sans-serif'
	},
	sections: []
}

interface PageBuilderSettings {
	showSchemaEditor: boolean
	showSchema: boolean
	autoSave: boolean
	showAIAssistant: boolean
}

const defaultSettings: PageBuilderSettings = {
	showSchemaEditor: true,
	showSchema: false,
	autoSave: false,
	showAIAssistant: true
}

export default function Playground({ schema: defaultSchema }: { schema?: CustomPageSchema }) {
	const [savedSchema, setSavedSchema] = useLocalStorage<CustomPageSchema>(
		'custom-page-config',
		defaultSchema ?? emptySchema
	)
	const [settings, setSettings] = useLocalStorage<PageBuilderSettings>(
		'page-builder-settings',
		defaultSettings
	)
	const [schema, dispatch, history] = useCustomPageSchemaReducer(savedSchema)
	const [inputSchema, setInputSchema] = useState<string>(JSON.stringify(schema, null, 2))
	const [isValidJson, setIsValidJson] = useState(true)
	const [isAILoading, setIsAILoading] = useState(false)

	useEffect(() => {
		setInputSchema(JSON.stringify(schema, null, 2))
	}, [schema])

	useAutosave({
		data: schema,
		onSave: setSavedSchema,
		enabled: settings.autoSave
	})

	const handleApplySchema = () => {
		try {
			const parsed = JSON.parse(inputSchema)
			dispatch({ type: 'SET_SCHEMA', payload: parsed })
			setIsValidJson(true)
		} catch (e) {
			setIsValidJson(false)
		}
	}

	return (
		<>
			<div className="overflow-y-auto top-0 left-0 z-10 p-8 pt-0 w-full max-w-full bg-white border-r prose prose-zinc border-zinc-200 prose-headings:font-sans md:absolute md:h-screen md:w-1/2">
				<div className="sticky top-0 z-10 pt-2 pb-6 mb-6 bg-white">
					<div className="flex justify-between items-center mb-4">
						<div className="flex gap-2 items-center">
							<h2 className="my-0 text-xl font-medium">Page Builder</h2>
							<span className="rounded-full border border-zinc-200 bg-zinc-100 px-2 py-0.5 text-[0.65rem] font-medium text-zinc-500">
								2.0
							</span>
						</div>
						<div className="flex gap-2 items-center">
							<button
								onClick={history.undo}
								disabled={!history.canUndo}
								className="grid place-content-center p-2 w-8 h-8 rounded-md border transition-colors border-zinc-200 text-zinc-600 hover:border-zinc-300 hover:bg-zinc-50 disabled:opacity-50"
								title="Undo"
							>
								↩
							</button>
							<button
								onClick={history.redo}
								disabled={!history.canRedo}
								className="grid place-content-center p-2 w-8 h-8 rounded-md border transition-colors border-zinc-200 text-zinc-600 hover:border-zinc-300 hover:bg-zinc-50 disabled:opacity-50"
								title="Redo"
							>
								↪
							</button>
							<div className="w-px h-4 bg-zinc-200" />
							<button
								onClick={history.reset}
								className="rounded-md border border-red-200 px-3 py-1.5 text-sm font-medium text-red-600 transition-colors hover:bg-red-50"
							>
								Reset changes
							</button>
							<button
								onClick={() => {
									dispatch({ type: 'SET_SCHEMA', payload: emptySchema })
								}}
								className="rounded-md border border-red-200 px-3 py-1.5 text-sm font-medium text-red-600 transition-colors hover:bg-red-50"
							>
								Clear page
							</button>
							<div className="w-px h-4 bg-zinc-200" />
							<button
								onClick={() => {
									setInputSchema(JSON.stringify(schema, null, 2))
									navigator.clipboard.writeText(JSON.stringify(schema, null, 2))
									setSavedSchema(schema)
								}}
								className="px-4 py-2 text-sm font-medium text-white rounded-md transition-colors bg-zinc-900 hover:bg-zinc-800"
							>
								Save and copy
							</button>
						</div>
					</div>
					<div className="flex flex-col gap-3 p-4 rounded-lg border border-zinc-200">
						<div className="flex justify-between items-center">
							<span className="text-sm font-medium">Schema Editor</span>
							<button
								onClick={() =>
									setSettings((prev) => ({ ...prev, showSchemaEditor: !prev.showSchemaEditor }))
								}
								className={cn(
									'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
									settings.showSchemaEditor ? 'bg-zinc-900' : 'bg-zinc-200'
								)}
							>
								<span
									className={cn(
										'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
										settings.showSchemaEditor ? 'translate-x-6' : 'translate-x-1'
									)}
								/>
							</button>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm font-medium">AI Assistant</span>
							<button
								onClick={() =>
									setSettings((prev) => ({ ...prev, showAIAssistant: !prev.showAIAssistant }))
								}
								className={cn(
									'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
									settings.showAIAssistant ? 'bg-zinc-900' : 'bg-zinc-200'
								)}
							>
								<span
									className={cn(
										'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
										settings.showAIAssistant ? 'translate-x-6' : 'translate-x-1'
									)}
								/>
							</button>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm font-medium">Raw Schema</span>
							<button
								onClick={() => setSettings((prev) => ({ ...prev, showSchema: !prev.showSchema }))}
								className={cn(
									'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
									settings.showSchema ? 'bg-zinc-900' : 'bg-zinc-200'
								)}
							>
								<span
									className={cn(
										'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
										settings.showSchema ? 'translate-x-6' : 'translate-x-1'
									)}
								/>
							</button>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm font-medium">Auto Save</span>
							<button
								onClick={() => setSettings((prev) => ({ ...prev, autoSave: !prev.autoSave }))}
								className={cn(
									'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
									settings.autoSave ? 'bg-zinc-900' : 'bg-zinc-200'
								)}
							>
								<span
									className={cn(
										'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
										settings.autoSave ? 'translate-x-6' : 'translate-x-1'
									)}
								/>
							</button>
						</div>
					</div>
				</div>

				{settings.showSchema && (
					<div className="p-4 mb-6 rounded-lg border border-zinc-200">
						<div className="flex justify-between items-center mb-4">
							<h3 className="m-0 text-sm font-medium">Raw Schema</h3>
							<button
								onClick={handleApplySchema}
								disabled={!isValidJson}
								className="rounded-md bg-zinc-900 px-3 py-1.5 text-sm font-medium text-white transition-colors hover:bg-zinc-800 disabled:opacity-50"
							>
								Apply changes
							</button>
						</div>
						<textarea
							value={inputSchema}
							onChange={(event) => {
								setInputSchema(event.target.value)
								try {
									JSON.parse(event.target.value)
									setIsValidJson(true)
								} catch (e) {
									setIsValidJson(false)
								}
							}}
							className={cn(
								'p-3 w-full h-48 text-sm rounded-md border transition-colors',
								isValidJson ? 'border-zinc-200' : 'bg-red-50 border-red-200'
							)}
						/>
					</div>
				)}

                {settings.showSchemaEditor && (
                    <div className="mb-6">
                        <SchemaEditor schema={schema} dispatch={dispatch} />
                    </div>
                )}
                {settings.showAIAssistant && (
                    <AIAssistant schema={schema} dispatch={dispatch} onLoadingChange={setIsAILoading} />
                )}
			</div>

			<div
				className={cn(
					'h-[200vh] w-full origin-top-right scale-50 overflow-x-hidden overflow-y-scroll max-md:hidden',
					isAILoading &&
						'relative after:absolute after:inset-0 after:animate-pulse after:bg-zinc-900/5'
				)}
			>
				{schema.sections.length === 0 ? (
					<div className="flex flex-col gap-4 justify-center items-center p-8">
						<h2 className="mt-16 scale-[2] text-center text-zinc-500">
							Start by editing the schema on the left
						</h2>
					</div>
				) : (
					<CustomPageBuilder schema={schema} />
				)}
			</div>
		</>
	)
}
