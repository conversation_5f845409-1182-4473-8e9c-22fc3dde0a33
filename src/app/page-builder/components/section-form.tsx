import { SectionProps } from '@/app/components/custom-page-components'
import { CustomPageSchemaReducerAction } from '@/app/components/custom-page-components/reducer'
import { Field } from './field'
import { Dispatch, useCallback } from 'react'

export interface SectionFormProps {
	id: string
	props: SectionProps
	dispatch: Dispatch<CustomPageSchemaReducerAction>
	index: number
}

export function SectionForm({ id, props, dispatch, index }: SectionFormProps) {
	const changeTitle = useCallback(
		(event: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement>) =>
			dispatch({
				type: 'SET_SECTION_PROP',
				index,
				key: 'title',
				payload: event.target.value
			}),
		[]
	)
	return (
		<div className="section-card">
			<div className="space-y-4">
				<Field
					id={`${id}-title`}
					label="Section Title"
					type="text"
					value={props?.title}
					onChange={changeTitle}
				/>
				<Field
					id={`${id}-background`}
					label="Background"
					type="color"
					inline
					value={props?.background}
					onChange={(event) =>
						dispatch({
							type: 'SET_SECTION_PROP',
							index,
							key: 'background',
							payload: event.target.value
						})
					}
				/>

				<Field
					id={id + ' Background Ring Color'}
					label="Background Ring Color"
					type="color"
					inline
					value={props?.backgroundRing}
					onChange={(event) =>
						dispatch({
							type: 'SET_SECTION_PROP',
							index,
							key: 'backgroundRing',
							payload: event.target.value
						})
					}
				/>
				<Field
					id={id + ' Background Rounded'}
					label="Background Rounded"
					type="checkbox"
					inline
					checked={props?.backgroundRounded}
					onChange={() =>
						dispatch({
							type: 'SET_SECTION_PROP',
							index,
							key: 'backgroundRounded',
							payload: !props?.backgroundRounded
						})
					}
				/>
				<Field
					id={id + ' Section ID'}
					label="Section ID"
					type="text"
					value={props?.id}
					onChange={(event) =>
						dispatch({
							type: 'SET_SECTION_PROP',
							index,
							key: 'id',
							payload: event.target.value
						})
					}
				/>
			</div>
		</div>
	)
}
