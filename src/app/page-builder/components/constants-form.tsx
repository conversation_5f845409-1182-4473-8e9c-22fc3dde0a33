import { CustomPageSchema } from '@/app/components/custom-page-components'
import { CustomPageSchemaReducerAction } from '@/app/components/custom-page-components/reducer'
import { Dispatch } from 'react'
import { Field } from './field'

export function ConstantsForm({
	schema,
	dispatch
}: {
	schema: CustomPageSchema
	dispatch: Dispatch<CustomPageSchemaReducerAction>
}) {
	return (
		<div className="space-y-4">
			<Field
				label="Background"
				type="color"
				inline
				value={schema?.constants?.background}
				onChange={(event) =>
					dispatch({
						type: 'SET_CONSTANT',
						key: 'background',
						payload: event.target.value
					})
				}
			/>
			<Field
				label="Accent Colors:"
				type="color"
				inline
				values={schema?.constants?.accentColors}
				onChanges={[
					(event) =>
						dispatch({
							type: 'SET_CONSTANT',
							key: 'accentColors',
							payload: [
								event.target.value,
								schema?.constants?.accentColors ? schema?.constants?.accentColors[1] : ''
							]
						}),
					(event) =>
						dispatch({
							type: 'SET_CONSTANT',
							key: 'accentColors',
							payload: [
								schema?.constants?.accentColors ? schema?.constants?.accentColors[0] : '',
								event.target.value
							]
						})
				]}
			/>
			<Field
				label="Muted Text Color:"
				type="color"
				inline
				value={schema?.constants?.mutedTextColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_CONSTANT',
						key: 'mutedTextColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				label="Text Color:"
				type="color"
				inline
				value={schema?.constants?.textColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_CONSTANT',
						key: 'textColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				label="Muted Background:"
				type="color"
				inline
				value={schema?.constants?.mutedBackground}
				onChange={(event) =>
					dispatch({
						type: 'SET_CONSTANT',
						key: 'mutedBackground',
						payload: event.target.value
					})
				}
			/>
			<Field
				label="Background Ring:"
				type="color"
				inline
				value={schema?.constants?.backgroundRing}
				onChange={(event) =>
					dispatch({
						type: 'SET_CONSTANT',
						key: 'backgroundRing',
						payload: event.target.value
					})
				}
			/>
			<Field
				label="Display Font Family:"
				type="text"
				value={schema?.constants?.displayFontFamily}
				onChange={(event) =>
					dispatch({
						type: 'SET_CONSTANT',
						key: 'displayFontFamily',
						payload: event.target.value
					})
				}
			/>
			<Field
				label="Default Font Family:"
				type="text"
				value={schema?.constants?.defaultFontFamily}
				onChange={(event) =>
					dispatch({
						type: 'SET_CONSTANT',
						key: 'defaultFontFamily',
						payload: event.target.value
					})
				}
			/>
		</div>
	)
}
