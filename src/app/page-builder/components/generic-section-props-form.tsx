import { CustomSectionProps } from '@/app/components/custom-page-components'
import { CustomPageSchemaReducerAction } from '@/app/components/custom-page-components/reducer'
import { Dispatch } from 'react'
import { HeroSectionForm } from './hero-section-form'
import { LogosSectionForm } from './logos-section-form'
import { IFrameSectionForm } from './iframe-section-form'
import { CardsSectionForm } from './cards-section-form'
import { DateTimeSectionForm } from './date-time-section-form'
import { RichTextSectionForm } from './rich-text-section-form'
import { LinksSectionForm } from './links-section-form'
import { TabsSectionForm } from './tabs-section-form'

export function GenericSectionPropsForm({
	section,
	dispatch,
	index,
	id
}: {
	section: CustomSectionProps
	dispatch: Dispatch<CustomPageSchemaReducerAction>
	index: number
	id: string
}) {
	switch (section.type) {
		case 'hero':
			return <HeroSectionForm props={section.props} dispatch={dispatch} index={index} id={id} />
		case 'logos':
			return <LogosSectionForm props={section.props} dispatch={dispatch} index={index} id={id} />
		case 'iframe':
			return <IFrameSectionForm props={section.props} dispatch={dispatch} index={index} id={id} />
		case 'cards':
			return <CardsSectionForm props={section.props} dispatch={dispatch} index={index} id={id} />
		case 'date-time':
			return <DateTimeSectionForm props={section.props} dispatch={dispatch} index={index} id={id} />
		case 'rich-text':
			return <RichTextSectionForm props={section.props} dispatch={dispatch} index={index} id={id} />
		case 'links':
			return <LinksSectionForm props={section.props} dispatch={dispatch} index={index} id={id} />
		case 'tabs':
			return <TabsSectionForm props={section.props} dispatch={dispatch} index={index} id={id} />
		default:
			return null
	}
}
