import { Dispatch, useState } from 'react'
import { Field } from './field'
import { LogosSectionProps } from '@/app/components/custom-page-components'
import { CustomPageSchemaReducerAction } from '@/app/components/custom-page-components/reducer'
import { ReorderModal } from './reorder-modal'

type Props = {
	id: string
	props: LogosSectionProps
	dispatch: Dispatch<CustomPageSchemaReducerAction>
	index: number
}
export function LogosSectionForm({ id, props, dispatch, index }: Props) {
	const [isReorderOpen, setIsReorderOpen] = useState(false)

	return (
		<div>
			<div className="mb-6 mt-8 flex items-center justify-between">
				<h4 className="my-0 text-sm font-medium text-zinc-900">Logos</h4>
				<button
					onClick={() => setIsReorderOpen(true)}
					disabled={props.logos.length < 2}
					className="text-sm font-medium text-zinc-500 transition-colors hover:text-zinc-900 disabled:opacity-50"
				>
					Reorder logos
				</button>
			</div>

			<div className="space-y-3">
				{props.logos.map((logo, logoIndex) => (
					<div
						key={logoIndex}
						className="flex flex-col gap-4 rounded-lg border border-zinc-200 p-4"
					>
						<div className="flex items-center justify-between">
							<span className="text-sm font-medium text-zinc-600">Logo {logoIndex + 1}</span>
							<button
								onClick={() => dispatch({ type: 'REMOVE_LOGO', index, logoIndex })}
								className="text-sm font-medium text-red-600 transition-colors hover:text-red-700"
							>
								Remove
							</button>
						</div>

						<div className="grid gap-4 sm:grid-cols-3">
							<Field
								id={`${id}-logo-${logoIndex}-image`}
								label="Image URL"
								type="text"
								value={logo.imgPath}
								onChange={(e) =>
									dispatch({
										type: 'SET_LOGO_PROP',
										index,
										logoIndex,
										key: 'imgPath',
										payload: e.target.value
									})
								}
							/>
							<Field
								id={`${id}-logo-${logoIndex}-url`}
								label="Link URL"
								type="text"
								value={logo.url}
								onChange={(e) =>
									dispatch({
										type: 'SET_LOGO_PROP',
										index,
										logoIndex,
										key: 'url',
										payload: e.target.value
									})
								}
							/>
							<Field
								id={`${id}-logo-${logoIndex}-name`}
								label="Name"
								type="text"
								value={logo.name}
								onChange={(e) =>
									dispatch({
										type: 'SET_LOGO_PROP',
										index,
										logoIndex,
										key: 'name',
										payload: e.target.value
									})
								}
							/>
						</div>
					</div>
				))}
			</div>

			<button
				onClick={() => dispatch({ type: 'ADD_LOGO', index })}
				disabled={
					(!props.logos.at(-1)?.imgPath || !props.logos.at(-1)?.url || !props.logos.at(-1)?.name) &&
					props.logos.length > 0
				}
				className="mt-4 flex w-full items-center justify-center gap-2 rounded-md border border-dashed border-zinc-200 py-3 text-sm font-medium text-zinc-500 transition-colors hover:border-zinc-300 hover:bg-zinc-50 disabled:opacity-50"
			>
				Add logo
			</button>

			<ReorderModal
				onReorder={(newItems) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'logos',
						payload: newItems.map((i) => i.value)
					})
				}
				items={props.logos.map((logo) => ({
					label: logo.name,
					value: logo
				}))}
				open={isReorderOpen}
				setOpen={setIsReorderOpen}
			/>
		</div>
	)
}
