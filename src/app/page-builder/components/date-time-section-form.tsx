import { DateTimeSectionProps } from '@/app/components/custom-page-components'
import { Field } from './field'
import { Dispatch, useState } from 'react'
import { CustomPageSchemaReducerAction } from '@/app/components/custom-page-components/reducer'
import { ReorderModal } from './reorder-modal'

export function DateTimeSectionForm({
	id,
	props,
	dispatch,
	index
}: {
	id: string
	props: DateTimeSectionProps
	dispatch: Dispatch<CustomPageSchemaReducerAction>
	index: number
}) {
	const [isReorderOpen, setIsReorderOpen] = useState(false)
	return (
		<div className="space-y-4">
			<Field
				id={id + ' Text Color'}
				label="Text Color"
				type="color"
				inline
				value={props?.textColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'textColor',
						payload: event.target.value
					})
				}
			/>
			<div>
				<div className="mb-6 mt-8 flex items-center justify-between">
					<h4 className="my-0 text-sm font-medium text-zinc-900">Places and Dates</h4>
					<button
						onClick={() => setIsReorderOpen(true)}
						disabled={props.places.length < 2}
						className="text-sm font-medium text-zinc-500 transition-colors hover:text-zinc-900 disabled:opacity-50"
					>
						Reorder
					</button>
				</div>
				<ReorderModal
					onReorder={(newItems) =>
						dispatch({
							type: 'SET_SECTION_PROP',
							index,
							key: 'places',
							payload: newItems.map((i) => i.value)
						})
					}
					items={props.places.map((place) => ({
						label: place.name,
						value: place
					}))}
					open={isReorderOpen}
					setOpen={setIsReorderOpen}
				/>
				<div className="space-y-3">
					{props.places.map((place, placeIndex) => (
						<div
							key={placeIndex}
							className="flex flex-col gap-4 rounded-lg border border-zinc-200 p-4"
						>
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium text-zinc-600">Place {placeIndex + 1}</span>
								<button
									onClick={() => dispatch({ type: 'REMOVE_PLACE', index, placeIndex })}
									className="text-sm font-medium text-red-600 transition-colors hover:text-red-700"
								>
									Remove
								</button>
							</div>
							<Field
								id={id + ' Place Image'}
								label="Image URL"
								type="text"
								value={place.img}
								onChange={(event) =>
									dispatch({
										type: 'SET_PLACE_PROP',
										index,
										placeIndex,
										key: 'img',
										payload: event.target.value
									})
								}
							/>
							<Field
								id={id + ' Place Name'}
								label="Name"
								type="text"
								value={place.name}
								onChange={(event) =>
									dispatch({
										type: 'SET_PLACE_PROP',
										index,
										placeIndex,
										key: 'name',
										payload: event.target.value
									})
								}
							/>
							<Field
								id={id + ' Place Time'}
								label="Time"
								type="text"
								value={place.time}
								onChange={(event) =>
									dispatch({
										type: 'SET_PLACE_PROP',
										index,
										placeIndex,
										key: 'time',
										payload: event.target.value
									})
								}
							/>
							<Field
								id={id + ' Place Date'}
								label="Date"
								type="text"
								value={place.date}
								onChange={(event) =>
									dispatch({
										type: 'SET_PLACE_PROP',
										index,
										placeIndex,
										key: 'date',
										payload: event.target.value
									})
								}
							/>
							<Field
								id={id + ' Place Address'}
								label="Address"
								type="text"
								value={place.address}
								onChange={(event) =>
									dispatch({
										type: 'SET_PLACE_PROP',
										index,
										placeIndex,
										key: 'address',
										payload: event.target.value
									})
								}
							/>
						</div>
					))}
				</div>
				<button
					className="mt-4 flex w-full items-center justify-center gap-2 rounded-md border border-dashed border-zinc-200 py-3 text-sm font-medium text-zinc-500 transition-colors hover:border-zinc-300 hover:bg-zinc-50 disabled:opacity-50"
					onClick={() => dispatch({ type: 'ADD_PLACE', index })}
					disabled={
						(!props.places.at(-1)?.name ||
							!props.places.at(-1)?.date ||
							!props.places.at(-1)?.time) &&
						props.places.length > 0
					}
				>
					Add Place
				</button>
			</div>
		</div>
	)
}
