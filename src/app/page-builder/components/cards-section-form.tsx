import { CardsSectionProps } from '@/app/components/custom-page-components'
import { Field } from './field'
import { Dispatch, useState } from 'react'
import { CustomPageSchemaReducerAction } from '@/app/components/custom-page-components/reducer'
import { ReorderModal } from './reorder-modal'

export function CardsSectionForm({
	id,
	props,
	dispatch,
	index
}: {
	id: string
	props: CardsSectionProps
	dispatch: Dispatch<CustomPageSchemaReducerAction>
	index: number
}) {
	const [isReorderOpen, setIsReorderOpen] = useState(false)
	return (
		<div className="space-y-4">
			<Field
				id={id + ' Type'}
				label="Include Description"
				type="checkbox"
				inline
				checked={props?.type === 'descriptive'}
				onChange={() =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'type',
						payload: props?.type === 'short' ? 'descriptive' : 'short'
					})
				}
			/>
			{props?.type === 'descriptive' && (
				<Field
					id={id + ' Full Width'}
					label="Full Width"
					type="checkbox"
					inline
					checked={props?.fullWidth}
					onChange={() =>
						dispatch({
							type: 'SET_SECTION_PROP',
							index,
							key: 'fullWidth',
							payload: !props?.fullWidth
						})
					}
				/>
			)}
			<Field
				id={id + ' Card Background'}
				label="Card Background"
				type="color"
				inline
				value={props?.cardBackground}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'cardBackground',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Card Ring'}
				label="Card Ring"
				type="color"
				inline
				value={props?.cardRing}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'cardRing',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Card Text Color'}
				label="Card Text Color"
				type="color"
				inline
				value={props?.cardTextColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'cardTextColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Card Title Color'}
				label="Card Title Color"
				type="color"
				inline
				value={props?.cardTitleColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'cardTitleColor',
						payload: event.target.value
					})
				}
			/>
			<div>
				<div className="mb-6 mt-8 flex items-center justify-between">
					<h4 className="my-0 text-sm font-medium text-zinc-900">Cards</h4>
					<button
						onClick={() => setIsReorderOpen(true)}
						disabled={props.cardsData.length < 2}
						className="text-sm font-medium text-zinc-500 transition-colors hover:text-zinc-900 disabled:opacity-50"
					>
						Reorder cards
					</button>
				</div>
				<ReorderModal
					onReorder={(newItems) =>
						dispatch({
							type: 'SET_SECTION_PROP',
							index,
							key: 'cardsData',
							payload: newItems.map((i) => i.value)
						})
					}
					items={props.cardsData.map((card) => ({
						label: card.title,
						value: card
					}))}
					open={isReorderOpen}
					setOpen={setIsReorderOpen}
				/>
				<div className="space-y-3">
					{props.cardsData.map((card, cardIndex) => (
						<div
							key={cardIndex}
							className="flex flex-col gap-4 rounded-lg border border-zinc-200 p-4"
						>
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium text-zinc-600">Card {cardIndex + 1}</span>
								<button
									onClick={() => dispatch({ type: 'REMOVE_CARD', index, cardIndex })}
									className="text-sm font-medium text-red-600 transition-colors hover:text-red-700"
								>
									Remove
								</button>
							</div>
							<Field
								id={id + ' Card Image'}
								label="Image"
								type="text"
								value={card.imgPath}
								onChange={(event) =>
									dispatch({
										type: 'SET_CARD_PROP',
										index,
										cardIndex,
										key: 'imgPath',
										payload: event.target.value
									})
								}
							/>
							<Field
								id={id + ' Card Title'}
								label="Title"
								type="text"
								value={card.title}
								onChange={(event) =>
									dispatch({
										type: 'SET_CARD_PROP',
										index,
										cardIndex,
										key: 'title',
										payload: event.target.value
									})
								}
							/>
							<Field
								id={id + ' Card Name'}
								label="Name"
								type="text"
								value={card.name}
								onChange={(event) =>
									dispatch({
										type: 'SET_CARD_PROP',
										index,
										cardIndex,
										key: 'name',
										payload: event.target.value
									})
								}
							/>
							{props.type === 'descriptive' && (
								<Field
									id={id + ' Card Description'}
									label="Description"
									type="text"
									value={card.description}
									onChange={(event) =>
										dispatch({
											type: 'SET_CARD_PROP',
											index,
											cardIndex,
											key: 'description',
											payload: event.target.value
										})
									}
								/>
							)}
						</div>
					))}
				</div>
				<button
					className="mt-4 flex w-full items-center justify-center gap-2 rounded-md border border-dashed border-zinc-200 py-3 text-sm font-medium text-zinc-500 transition-colors hover:border-zinc-300 hover:bg-zinc-50 disabled:opacity-50"
					onClick={() => dispatch({ type: 'ADD_CARD', index })}
					disabled={
						(!props.cardsData.at(-1)?.imgPath ||
							!props.cardsData.at(-1)?.title ||
							(props.type === 'descriptive' && !props.cardsData.at(-1)?.description)) &&
						props.cardsData.length > 0
					}
				>
					Add Card
				</button>
			</div>
		</div>
	)
}
