import { IFrameSectionProps } from '@/app/components/custom-page-components'
import { Field } from './field'
import { Dispatch } from 'react'
import { CustomPageSchemaReducerAction } from '@/app/components/custom-page-components/reducer'

export function IFrameSectionForm({
	id,
	props,
	dispatch,
	index
}: {
	id: string
	props: IFrameSectionProps
	dispatch: Dispatch<CustomPageSchemaReducerAction>
	index: number
}) {
	return (
		<div className="space-y-4">
			<Field
				id={id + ' Form Active'}
				label="Form Active"
				type="checkbox"
				inline
				checked={props?.isFormActive}
				onChange={() =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'isFormActive',
						payload: !props?.isFormActive
					})
				}
			/>
			<Field
				id={id + ' Hide card'}
				label="Hide Card"
				type="checkbox"
				inline
				checked={props?.hideCard}
				onChange={() =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'hideCard',
						payload: !props?.hideCard
					})
				}
			/>
			<Field
				id={id + ' Typeform ID'}
				label="Typeform ID"
				type="text"
				value={props?.typeformId}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'typeformId',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Form Name'}
				label="Form Name"
				type="text"
				value={props?.formName}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'formName',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' HTML Content'}
				label="HTML Content"
				type="wysiwyg"
				value={props?.htmlContent}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'htmlContent',
						payload: event.target.value
					})
				}
			/>

			<Field
				id={id + ' Card Background'}
				label="Card Background"
				type="color"
				inline
				value={props?.cardBackground}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'cardBackground',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Card Ring'}
				label="Card Ring"
				type="color"
				inline
				value={props?.cardRing}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'cardRing',
						payload: event.target.value
					})
				}
			/>
		</div>
	)
}
