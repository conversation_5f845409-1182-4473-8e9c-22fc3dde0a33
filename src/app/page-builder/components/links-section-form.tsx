import { LinksSectionProps } from '@/app/components/custom-page-components'
import { CustomPageSchemaReducerAction } from '@/app/components/custom-page-components/reducer'
import { Dispatch, useState } from 'react'
import { Field } from './field'
import { ReorderModal } from './reorder-modal'
type Props = {
	id: string
	props: LinksSectionProps
	dispatch: Dispatch<CustomPageSchemaReducerAction>
	index: number
}
export function LinksSectionForm({ id, props, dispatch, index }: Props) {
	const [isReorderOpen, setIsReorderOpen] = useState(false)

	return (
		<div className="space-y-6">
			<div className="space-y-4 rounded-lg border border-zinc-200 p-4">
				<h4 className="m-0 text-sm font-medium text-zinc-900">Colors</h4>
				<div className="grid gap-4 sm:grid-cols-2">
					<Field
						id={id + '-link-bg-color'}
						label="Link Background"
						type="color"
						inline
						values={props?.linkBackgroundColor}
						onChanges={[
							(e) =>
								dispatch({
									type: 'SET_SECTION_PROP',
									index,
									key: 'linkBackgroundColor',
									payload: [e.target.value, props?.linkBackgroundColor?.[1] ?? '']
								}),
							(e) =>
								dispatch({
									type: 'SET_SECTION_PROP',
									index,
									key: 'linkBackgroundColor',
									payload: [props?.linkBackgroundColor?.[0] ?? '', e.target.value]
								})
						]}
					/>
					<Field
						id={id + '-link-text-color'}
						label="Link Text"
						type="color"
						inline
						value={props?.linkTextColor}
						onChange={(e) =>
							dispatch({
								type: 'SET_SECTION_PROP',
								index,
								key: 'linkTextColor',
								payload: e.target.value
							})
						}
					/>
				</div>
			</div>

			<div>
				<div className="mb-4 flex items-center justify-between">
					<h4 className="m-0 text-sm font-medium text-zinc-900">Links</h4>
					<button
						onClick={() => setIsReorderOpen(true)}
						disabled={props.links.length < 2}
						className="text-sm font-medium text-zinc-500 transition-colors hover:text-zinc-900 disabled:opacity-50"
					>
						Reorder links
					</button>
				</div>

				<div className="space-y-3">
					{props.links.map((link, linkIndex) => (
						<div
							key={linkIndex}
							className="flex flex-col gap-4 rounded-lg border border-zinc-200 p-4"
						>
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium text-zinc-600">Link {linkIndex + 1}</span>
								<button
									onClick={() => dispatch({ type: 'REMOVE_LINK', index, linkIndex })}
									className="text-sm font-medium text-red-600 transition-colors hover:text-red-700"
								>
									Remove
								</button>
							</div>

							<div className="grid gap-4 sm:grid-cols-3">
								<Field
									id={`${id}-link-${linkIndex}-url`}
									label="URL"
									type="text"
									value={link.url}
									onChange={(e) =>
										dispatch({
											type: 'SET_LINK_PROP',
											index,
											linkIndex,
											key: 'url',
											payload: e.target.value
										})
									}
								/>
								<Field
									id={`${id}-link-${linkIndex}-title`}
									label="Title"
									type="text"
									value={link.title}
									onChange={(e) =>
										dispatch({
											type: 'SET_LINK_PROP',
											index,
											linkIndex,
											key: 'title',
											payload: e.target.value
										})
									}
								/>
								<Field
									id={`${id}-link-${linkIndex}-text`}
									label="Text"
									type="text"
									value={link.linkText}
									onChange={(e) =>
										dispatch({
											type: 'SET_LINK_PROP',
											index,
											linkIndex,
											key: 'linkText',
											payload: e.target.value
										})
									}
								/>
							</div>
						</div>
					))}
				</div>

				<button
					onClick={() => dispatch({ type: 'ADD_LINK', index })}
					disabled={
						(!props.links.at(-1)?.url ||
							!props.links.at(-1)?.linkText ||
							!props.links.at(-1)?.title) &&
						props.links.length > 0
					}
					className="mt-4 flex w-full items-center justify-center gap-2 rounded-md border border-dashed border-zinc-200 py-3 text-sm font-medium text-zinc-500 transition-colors hover:border-zinc-300 hover:bg-zinc-50 disabled:opacity-50"
				>
					Add link
				</button>
			</div>

			<ReorderModal
				onReorder={(newItems) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'links',
						payload: newItems.map((i) => i.value)
					})
				}
				items={props.links.map((link) => ({
					label: link.title,
					value: link
				}))}
				open={isReorderOpen}
				setOpen={setIsReorderOpen}
			/>
		</div>
	)
}
