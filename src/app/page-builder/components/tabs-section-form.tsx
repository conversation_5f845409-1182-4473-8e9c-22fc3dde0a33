import { CustomPageSchemaReducerAction } from '@/app/components/custom-page-components/reducer'
import { Tab, TabsSectionProps } from '@/app/components/custom-page-components/tabs-section'
import { Dispatch, useState } from 'react'
import { Field } from './field'
import { ReorderModal } from './reorder-modal'

export function TabsSectionForm({
	id,
	props,
	dispatch,
	index
}: {
	id: string
	props: TabsSectionProps
	dispatch: Dispatch<CustomPageSchemaReducerAction>
	index: number
}) {
	const [isReorderOpen, setIsReorderOpen] = useState(false)
	return (
		<div className="space-y-4">
			<Field
				id={id + ' Tabs Background'}
				label="Tabs Background"
				type="color"
				inline
				value={props?.tabsBackground}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'tabsBackground',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Tabs Trigger Background'}
				label="Tabs Trigger Background"
				type="color"
				inline
				value={props?.tabsTriggerBackground}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'tabsTriggerBackground',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Tabs Trigger Text Color'}
				label="Tabs Trigger Text Color"
				type="color"
				inline
				value={props?.tabsTriggerTextColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'tabsTriggerTextColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Tabs Content Background'}
				label="Tabs Content Background"
				type="color"
				inline
				value={props?.tabsContentBackground}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'tabsContentBackground',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Tabs Content Text Color'}
				label="Tabs Content Text Color"
				type="color"
				inline
				value={props?.tabsContentTextColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'tabsContentTextColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Tabs Content Ring'}
				label="Tabs Content Ring"
				type="color"
				inline
				value={props?.tabsContentRing}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'tabsContentRing',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Tabs Ring'}
				label="Tabs Ring"
				type="color"
				inline
				value={props?.tabsRing}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'tabsRing',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Table Header Text Color'}
				label="Table Header Text Color"
				type="color"
				inline
				value={props?.tableProps?.headerTextColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_TABS_SECTION_TABLE_PROPS',
						index,
						key: 'headerTextColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Table Body Text Color'}
				label="Table Body Text Color"
				type="color"
				inline
				value={props?.tableProps?.bodyTextColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_TABS_SECTION_TABLE_PROPS',
						index,
						key: 'bodyTextColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Table Even Row Background'}
				label="Table Even Row Background"
				type="color"
				inline
				value={props?.tableProps?.evenRowBackground}
				onChange={(event) =>
					dispatch({
						type: 'SET_TABS_SECTION_TABLE_PROPS',
						index,
						key: 'evenRowBackground',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Table Border Color'}
				label="Table Border Color"
				type="color"
				inline
				value={props?.tableProps?.borderColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_TABS_SECTION_TABLE_PROPS',
						index,
						key: 'borderColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Content Max Width'}
				label="Content Max Width"
				type="checkbox"
				inline
				checked={props?.maxWidth ?? false}
				onChange={() =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'maxWidth',
						payload: !props?.maxWidth
					})
				}
			/>
			<div>
				<div className="mb-6 mt-8 flex items-center justify-between">
					<h4 className="my-0 text-sm font-medium text-zinc-900">Tabs</h4>
					<button
						onClick={() => setIsReorderOpen(true)}
						disabled={props.tabs.length < 2}
						className="text-sm font-medium text-zinc-500 transition-colors hover:text-zinc-900 disabled:opacity-50"
					>
						Reorder tabs
					</button>
				</div>
				<ReorderModal
					onReorder={(newItems) =>
						dispatch({
							type: 'SET_SECTION_PROP',
							index,
							key: 'tabs',
							payload: newItems.map((i) => i.value)
						})
					}
					items={props.tabs.map((tab) => ({
						label: tab.title,
						value: tab
					}))}
					open={isReorderOpen}
					setOpen={setIsReorderOpen}
				/>
				<div className="space-y-3">
					{props.tabs.map((tab, tabIndex) => (
						<TabForm
							key={tabIndex}
							id={id}
							tab={tab}
							dispatch={dispatch}
							index={index}
							tabIndex={tabIndex}
						/>
					))}
					<button
						className="mt-4 flex w-full items-center justify-center gap-2 rounded-md border border-dashed border-zinc-200 py-3 text-sm font-medium text-zinc-500 transition-colors hover:border-zinc-300 hover:bg-zinc-50 disabled:opacity-50"
						onClick={() => dispatch({ type: 'ADD_TAB', index })}
						disabled={!props.tabs.at(-1)?.title}
					>
						Add Tab
					</button>
				</div>
			</div>
		</div>
	)
}

function TabForm({
	id,
	tab,
	dispatch,
	index,
	tabIndex
}: {
	id: string
	tab: Tab
	dispatch: Dispatch<CustomPageSchemaReducerAction>
	index: number
	tabIndex: number
}) {
	const [isTableRowReorderOpen, setIsTableRowReorderOpen] = useState(false)
	return (
		<div className="flex flex-col gap-4 rounded-lg border border-zinc-200 p-4">
			<div className="flex items-center justify-between">
				<span className="text-sm font-medium text-zinc-600">Tab {tabIndex + 1}</span>
				<button
					onClick={() =>
						dispatch({
							type: 'REMOVE_TAB',
							index,
							tabIndex
						})
					}
					className="text-sm font-medium text-red-600 transition-colors hover:text-red-700"
				>
					Remove tab
				</button>
			</div>
			<Field
				id={id + ' Tab Title'}
				label="Title"
				type="text"
				value={tab.title}
				onChange={(event) =>
					dispatch({
						type: 'SET_TAB_PROP',
						index,
						tabIndex,
						key: 'title',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Tab Table Switch'}
				label="Table"
				type="checkbox"
				inline
				checked={tab.isTable ?? false}
				onChange={() => {
					dispatch({
						type: 'SET_TAB_PROP',
						index,
						tabIndex,
						key: 'isTable',
						payload: !tab.isTable
					})

					if (!tab.isTable) {
						dispatch({
							type: 'SET_TAB_PROP',
							index,
							tabIndex,
							key: 'htmlContent',
							payload: ''
						})
					}
				}}
			/>
			{tab.isTable ? (
				<>
					<Field
						id={id + ' Tab Table Header'}
						label="Table Header"
						type="text"
						value={tab.tableData?.headers?.join(',') ?? ''}
						onChange={(event) =>
							dispatch({
								type: 'SET_TAB_PROP',
								index,
								tabIndex,
								key: 'tableData',
								payload: {
									...tab.tableData,
									headers: event.target.value.split(',')
								}
							})
						}
					/>
					<div>
						<div className="mb-6 mt-8 flex items-center justify-between">
							<h4 className="my-0 text-sm font-medium text-zinc-900">Table Rows</h4>
							<button
								onClick={() => setIsTableRowReorderOpen(true)}
								disabled={!tab.tableData?.rows || tab.tableData?.rows.length < 2}
								className="text-sm font-medium text-zinc-500 transition-colors hover:text-zinc-900 disabled:opacity-50"
							>
								Reorder rows
							</button>
						</div>
						<ReorderModal
							onReorder={(newItems) =>
								dispatch({
									type: 'SET_TAB_PROP',
									index,
									tabIndex,
									key: 'tableData',
									payload: {
										...tab.tableData,
										rows: newItems.map((i) => i.value)
									}
								})
							}
							items={(tab.tableData?.rows ?? []).map((row) => ({
								label: Array.isArray(row) ? row.join(', ') : row,
								value: row
							}))}
							open={isTableRowReorderOpen}
							setOpen={setIsTableRowReorderOpen}
						/>
						<div className="space-y-3">
							{tab.tableData?.rows?.map((row, rowIndex) => (
								<div
									key={rowIndex}
									className="flex flex-col gap-4 rounded-lg border border-zinc-200 p-4"
								>
									<div className="flex items-center justify-between">
										<span className="text-sm font-medium text-zinc-600">Row {rowIndex + 1}</span>
										<button
											onClick={() =>
												dispatch({
													type: 'SET_TAB_PROP',
													index,
													tabIndex,
													key: 'tableData',
													payload: {
														...tab.tableData,
														rows: tab.tableData?.rows.filter((_, i) => i !== rowIndex)
													}
												})
											}
											className="text-sm font-medium text-red-600 transition-colors hover:text-red-700"
										>
											Remove row
										</button>
									</div>
									{tab.tableData?.headers?.map((header, headerIndex) => (
										<Field
											key={headerIndex}
											id={id + ' Tab Table Row ' + rowIndex + ' Header ' + headerIndex}
											label={header}
											type="text"
											value={row[headerIndex]}
											onChange={(event) =>
												dispatch({
													type: 'SET_TAB_PROP',
													index,
													tabIndex,
													key: 'tableData',
													payload: {
														...tab.tableData,
														rows: tab.tableData?.rows.map((r, i) =>
															i === rowIndex
																? r.map((_, j) => (j === headerIndex ? event.target.value : r[j]))
																: r
														)
													}
												})
											}
										/>
									))}
								</div>
							))}
							<button
								className="mt-4 flex w-full items-center justify-center gap-2 rounded-md border border-dashed border-zinc-200 py-3 text-sm font-medium text-zinc-500 transition-colors hover:border-zinc-300 hover:bg-zinc-50 disabled:opacity-50"
								onClick={() =>
									dispatch({
										type: 'SET_TAB_PROP',
										index,
										tabIndex,
										key: 'tableData',
										payload: {
											...tab.tableData,
											rows: [...(tab.tableData?.rows ?? []), tab.tableData?.headers?.map(() => '')]
										}
									})
								}
								disabled={
									(!tab.tableData?.rows?.at(-1)?.some((cell) => cell) &&
										(tab.tableData?.rows?.length ?? 0) > 0) ||
									!tab.tableData?.headers?.length
								}
							>
								Add Row
							</button>
						</div>
					</div>
				</>
			) : (
				<Field
					id={id + ' Tab Content'}
					label="Content"
					type="wysiwyg"
					value={tab.htmlContent}
					onChange={(event) =>
						dispatch({
							type: 'SET_TAB_PROP',
							index,
							tabIndex,
							key: 'htmlContent',
							payload: event.target.value
						})
					}
				/>
			)}
		</div>
	)
}
