import { cn } from '@/utils/style'
import { Editor } from '@tinymce/tinymce-react'
import { useMemo, useState } from 'react'
import '../styles/forms.css'

export function Field({
	id,
	label,
	type,
	value,
	multiple,
	values,
	checked,
	onChange,
	onChanges,
	inline,
	initialValue,
	initialValues
}: {
	id?: string
	label: string
	type: string | 'wysiwyg' | 'table'
	value?: string | number
	multiple?: boolean
	values?: string[]
	checked?: boolean
	onChange?: (
		event: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement>
	) => void
	onChanges?: ((event: React.ChangeEvent<HTMLInputElement>) => void)[]
	inline?: boolean
	initialValue?: string | number
	initialValues?: string[]
}) {
	const isColorInput = type === 'color'
	const [isColorTypeOn, setIsColorTypeOn] = useState(
		isColorInput &&
			(value || values) &&
			!(value?.toString().startsWith('#') || values?.[0]?.toString().startsWith('#'))
			? false
			: true
	)
	const [isEditorVisible, setIsEditorVisible] = useState(true)
	// TODO IDK WHAT TO DO HERE
	// if (!value && !multiple) {
	// 	console.log('whatting')
	// 	value = initialValue
	// }
	// values = useMemo(() => values ?? initialValues, [values, initialValues])
	initialValue = useMemo(() => initialValue ?? value, [])
	initialValues = useMemo(() => initialValues ?? values, [])
	// set the value so its not undefined //TODO (#ffff00)
	// if (isColorInput && !value && !values) value = '#000000'
	// if (!checked) checked = false
	// if (type === 'text' && !value) value = ''
	// if (value && !onChange) onChange = () => {}
	// if (values && !onChanges) onChanges = [() => {}, () => {}]

	function resetValue() {
		if (multiple) {
			onChanges?.forEach((onC, index) => {
				onC({
					target: {
						value: initialValues?.[index]
					}
				} as React.ChangeEvent<HTMLInputElement>)
			})
			values = initialValues
		} else {
			onChange?.({
				target: {
					value: initialValue
				}
			} as React.ChangeEvent<HTMLInputElement>)
			value = initialValue
		}
	}

	if (type === 'wysiwyg' || type === 'table')
		return (
			<fieldset className={cn('field', inline && 'inline')}>
				<div className="mb-2 flex items-center justify-between">
					<label htmlFor={id ? id : label} className={cn(inline && 'mr-2 w-36')}>
						{label}
					</label>
					<button
						onClick={() => setIsEditorVisible((prev) => !prev)}
						className="rounded border border-zinc-200 px-2 py-1 text-sm hover:bg-zinc-50"
					>
						{isEditorVisible ? 'Hide Editor' : 'Show Editor'}
					</button>
				</div>
				{isEditorVisible ? (
					<Editor
						id={id ? id : label}
						apiKey={process.env.NEXT_PUBLIC_TINYMCE_API_KEY}
						value={value?.toString() ?? ''}
						init={{
							height: 500,
							menubar: true,
							plugins:
								type === 'table'
									? ['table']
									: [
											'advlist',
											'autolink',
											'lists',
											'link',
											'image',
											'charmap',
											'preview',
											'anchor',
											'searchreplace',
											'visualblocks',
											'code',
											'fullscreen',
											'insertdatetime',
											'media',
											'table',
											'code',
											'help',
											'wordcount'
									  ],
							toolbar:
								type === 'table'
									? 'undo redo table'
									: 'undo redo | blocks | ' +
									  'bold italic forecolor | alignleft aligncenter ' +
									  'alignright alignjustify | bullist numlist outdent indent | table ' +
									  'removeformat | help',
							content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
							id: id ? id : label
						}}
						onEditorChange={(content) => onChange?.({ target: { value: content } } as any)}
					/>
				) : (
					<textarea
						className="min-h-[100px] w-full rounded border p-2"
						value={value?.toString() ?? ''}
						onChange={(e) => onChange?.({ target: { value: e.target.value } } as any)}
						placeholder="Editor hidden to save resources. Click 'Show Editor' to edit with TinyMCE."
					/>
				)}
			</fieldset>
		)
	return (
		<fieldset className={cn('field', inline && isColorTypeOn && 'inline')}>
			<label
				htmlFor={id ?? label}
				className={cn('flex items-center justify-between', inline && isColorTypeOn && 'w-36')}
			>
				<span>{label}</span>
				<button
					className={cn(
						'text-sm font-medium text-zinc-500 transition-colors hover:text-zinc-900',
						((inline && isColorTypeOn) || (isColorTypeOn && isColorInput)) && 'hidden'
					)}
					onClick={resetValue}
				>
					Reset
				</button>
			</label>
			{values ? (
				<div className="flex gap-2">
					{values?.map((v, index) => (
						<input
							key={index}
							type={isColorInput ? (isColorTypeOn ? 'color' : 'text') : type}
							value={v}
							onChange={onChanges?.[index]}
							className="input"
						/>
					))}
				</div>
			) : (
				<input
					id={id ?? label}
					type={isColorInput ? (isColorTypeOn ? 'color' : 'text') : type}
					value={value}
					{...(checked && { checked })}
					onChange={onChange}
					className="input"
				/>
			)}
			{isColorInput && (
				<button
					onClick={() => setIsColorTypeOn((prev) => !prev)}
					className={cn(
						'button button-secondary ',
						isColorTypeOn ? 'ml-2 grid h-9 w-9 place-content-center p-0' : 'ml-2'
					)}
				>
					{isColorTypeOn ? (
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="16"
							height="16"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
							strokeLinecap="round"
							strokeLinejoin="round"
							className="mx-auto"
						>
							<polyline points="4 7 4 4 20 4 20 7" />
							<line x1="9" x2="15" y1="20" y2="20" />
							<line x1="12" x2="12" y1="4" y2="20" />
						</svg>
					) : (
						'Switch to color picker'
					)}
				</button>
			)}
			<button
				className={cn(
					'text-sm font-medium text-zinc-500 transition-colors hover:text-zinc-900',
					!((inline && isColorTypeOn) || (isColorTypeOn && isColorInput)) && 'hidden'
				)}
				onClick={resetValue}
			>
				Reset
			</button>
		</fieldset>
	)
}
