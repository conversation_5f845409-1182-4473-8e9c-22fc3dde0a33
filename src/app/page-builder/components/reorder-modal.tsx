import { Dialog, Transition } from '@headlessui/react'
import { Reorder, useMotionValue } from 'framer-motion'
import { Fragment, ReactNode, useEffect, useState } from 'react'

export function ReorderModal<T extends { label: string; value: unknown }>({
	items,
	onReorder,
	open,
	setOpen
}: {
	items: T[]
	onReorder: (items: T[]) => void
	open: boolean
	setOpen: (value: boolean) => void
}) {
	const [reorderedItems, setReorderedItems] = useState<T[]>(items)

	useEffect(() => {
		setReorderedItems(items)
	}, [items])

	return (
		<Transition appear show={open} as={Fragment}>
			<Dialog as="div" className="relative z-50" onClose={() => setOpen(false)}>
				<Transition.Child
					as={Fragment}
					enter="ease-out duration-300"
					enterFrom="opacity-0"
					enterTo="opacity-100"
					leave="ease-in duration-200"
					leaveFrom="opacity-100"
					leaveTo="opacity-0"
				>
					<div className="fixed inset-0 bg-black/50" />
				</Transition.Child>

				<div className="fixed inset-0 overflow-y-auto">
					<div className="flex min-h-full items-center justify-center p-4">
						<Dialog.Panel className="w-full max-w-xl transform overflow-hidden rounded-2xl bg-white p-6 shadow-xl transition-all">
							<Dialog.Title className="text-lg font-medium text-zinc-900">
								Reorder Sections
							</Dialog.Title>

							<Reorder.Group
								values={reorderedItems}
								onReorder={setReorderedItems}
								className="mt-4 space-y-2"
							>
								{reorderedItems.map((item) => (
									<ReorderItem key={item.label} value={item} id={`reorder-${item.label}`}>
										<div className="rounded-md border border-zinc-200 bg-white p-3 text-sm">
											{item.label}
										</div>
									</ReorderItem>
								))}
							</Reorder.Group>

							<div className="mt-6 flex justify-end gap-3">
								<button onClick={() => setOpen(false)} className="button button-secondary">
									Cancel
								</button>
								<button
									onClick={() => {
										onReorder(reorderedItems)
										setOpen(false)
									}}
									className="button button-primary"
								>
									Save Order
								</button>
							</div>
						</Dialog.Panel>
					</div>
				</div>
			</Dialog>
		</Transition>
	)
}

function ReorderItem({
	value,
	id,
	key,
	children
}: {
	value: unknown
	id: string
	key: string
	children: ReactNode
}) {
	const y = useMotionValue(0)
	return (
		<Reorder.Item value={value} id={id} key={key} style={{ y }}>
			{children}
		</Reorder.Item>
	)
}
