import { RichTextSectionProps } from '@/app/components/custom-page-components'
import { Field } from './field'
import { Dispatch, useState } from 'react'
import { CustomPageSchemaReducerAction } from '@/app/components/custom-page-components/reducer'

export function RichTextSectionForm({
	id,
	props,
	dispatch,
	index
}: {
	id: string
	props: RichTextSectionProps
	dispatch: Dispatch<CustomPageSchemaReducerAction>
	index: number
}) {
	return (
		<div className="space-y-4">
			<Field
				id={id + ' Text Color'}
				label="Text Color"
				type="color"
				inline
				value={props?.richTextProps?.textColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_RICH_TEXT_SECTION_PROP',
						index,
						key: 'textColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Strong Text Color'}
				label="Strong Text Color"
				type="color"
				inline
				value={props?.richTextProps?.strongTextColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_RICH_TEXT_SECTION_PROP',
						index,
						key: 'strongTextColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Strong Font Family'}
				label="Strong Font Family"
				type="text"
				value={props?.richTextProps?.strongFontFamily}
				onChange={(event) =>
					dispatch({
						type: 'SET_RICH_TEXT_SECTION_PROP',
						index,
						key: 'strongFontFamily',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Strong Font Weight'}
				label="Strong Font Weight"
				type="number"
				value={props?.richTextProps?.strongFontWeight ?? 600}
				onChange={(event) =>
					dispatch({
						type: 'SET_RICH_TEXT_SECTION_PROP',
						index,
						key: 'strongFontWeight',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Content'}
				label="Content"
				type="wysiwyg"
				value={props?.richTextProps?.content}
				onChange={(event) =>
					dispatch({
						type: 'SET_RICH_TEXT_SECTION_PROP',
						index,
						key: 'content',
						payload: event.target.value
					})
				}
			/>
		</div>
	)
}
