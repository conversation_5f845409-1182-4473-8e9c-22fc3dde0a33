import { HeroSectionProps } from '@/app/components/custom-page-components'
import { Dispatch } from 'react'
import { Field } from './field'
import { CustomPageSchemaReducerAction } from '@/app/components/custom-page-components/reducer'

export function HeroSectionForm({
	id,
	props,
	dispatch,
	index
}: {
	id: string
	props: HeroSectionProps
	dispatch: Dispatch<CustomPageSchemaReducerAction>
	index: number
}) {
	//TODO switch this to constants (#ffff00)
	props.ctaBgColor = props.ctaBgColor ?? ['#000000', '#000000']
	return (
		<div className="space-y-4">
			<Field
				id={id + ' Section Title'}
				label="Title"
				type="text"
				value={props?.title}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'title',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Description'}
				label="Description"
				type="text"
				value={props?.description}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'description',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Background'}
				label="Background"
				type="color"
				inline
				value={props?.background}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'background',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Image URL'}
				label="Image URL"
				type="text"
				value={props?.imageUrl}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'imageUrl',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Image Width'}
				label="Image Width"
				type="number"
				value={props?.imageWidth}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'imageWidth',
						payload: Number(event.target.value)
					})
				}
			/>
			<Field
				id={id + ' Image Height'}
				label="Image Height"
				type="number"
				value={props?.imageHeight}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'imageHeight',
						payload: Number(event.target.value)
					})
				}
			/>
			<Field
				id={id + ' Image Offset X'}
				label="Image Offset X"
				type="number"
				value={props?.imageOffsetX}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'imageOffsetX',
						payload: Number(event.target.value)
					})
				}
			/>
			<Field
				id={id + ' Image Offset Y'}
				label="Image Offset Y"
				type="number"
				value={props?.imageOffsetY}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'imageOffsetY',
						payload: Number(event.target.value)
					})
				}
			/>
			<Field
				id={id + ' CTA'}
				label="CTA"
				type="text"
				value={props?.cta}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'cta',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' CTA Text Color'}
				label="CTA Text Color"
				type="color"
				inline
				value={props?.ctaTextColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'ctaTextColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' CTA Background Color'}
				label="CTA Background Color"
				type="color"
				inline
				values={props?.ctaBgColor}
				onChanges={[
					(event) =>
						dispatch({
							type: 'SET_SECTION_PROP',
							index,
							key: 'ctaBgColor',
							payload: [event.target.value, props?.ctaBgColor ? props?.ctaBgColor[1] : '']
						}),
					(event) =>
						dispatch({
							type: 'SET_SECTION_PROP',
							index,
							key: 'ctaBgColor',
							payload: [props?.ctaBgColor ? props?.ctaBgColor[0] : '', event.target.value]
						})
				]}
			/>
			<Field
				id={id + ' Title Color'}
				label="Title Color"
				type="color"
				inline
				value={props?.titleColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'titleColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Description Color'}
				label="Description Color"
				type="color"
				inline
				value={props?.descriptionColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'descriptionColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Text Shadow'}
				label="Text Shadow"
				type="checkbox"
				inline
				checked={props?.textShadow}
				onChange={() =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'textShadow',
						payload: !props?.textShadow
					})
				}
			/>
			<Field
				id={id + ' Font Family'}
				label="Font Family"
				type="text"
				value={props?.fontFamily}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'fontFamily',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' CTA Href'}
				label="CTA Href"
				type="text"
				value={props?.ctaHref}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_PROP',
						index,
						key: 'ctaHref',
						payload: event.target.value
					})
				}
			/>
		</div>
	)
}
