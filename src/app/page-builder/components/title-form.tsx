import { SectionTitleProps } from '@/app/components/custom-page-components'
import { Field } from './field'
import { CustomPageSchemaReducerAction } from '@/app/components/custom-page-components/reducer'
import { Dispatch } from 'react'

export function TitleForm({
	id,
	props,
	dispatch,
	index
}: {
	id: string
	props: Omit<SectionTitleProps, 'content'>
	dispatch: Dispatch<CustomPageSchemaReducerAction>
	index: number
}) {
	return (
		<div className="space-y-4">
			<Field
				id={id + ' Hide Rectangle'}
				label="Hide Rectangle"
				type="checkbox"
				inline
				checked={props?.hideRectangle ?? false}
				onChange={() =>
					dispatch({
						type: 'SET_SECTION_TITLE_PROP',
						index,
						key: 'hideRectangle',
						payload: !props?.hideRectangle
					})
				}
			/>
			<Field
				id={id + ' Background Color'}
				label="Background Color"
				type="color"
				inline
				// TODO same thing here, sometimes the value is undefined (#ffff00)
				values={props?.bgColor ?? ['#000000', '#000000']}
				onChanges={[
					(event) =>
						dispatch({
							type: 'SET_SECTION_TITLE_PROP',
							index,
							key: 'bgColor',
							payload: [event.target.value, props?.bgColor ? props?.bgColor[1] : '']
						}),
					(event) =>
						dispatch({
							type: 'SET_SECTION_TITLE_PROP',
							index,
							key: 'bgColor',
							payload: [props?.bgColor ? props?.bgColor[0] : '', event.target.value]
						})
				]}
			/>
			<Field
				id={id + ' Text Color'}
				label="Text Color"
				type="color"
				inline
				value={props?.textColor}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_TITLE_PROP',
						index,
						key: 'textColor',
						payload: event.target.value
					})
				}
			/>
			<Field
				id={id + ' Font Family'}
				label="Font Family"
				type="text"
				value={props?.fontFamily}
				onChange={(event) =>
					dispatch({
						type: 'SET_SECTION_TITLE_PROP',
						index,
						key: 'fontFamily',
						payload: event.target.value
					})
				}
			/>
		</div>
	)
}
