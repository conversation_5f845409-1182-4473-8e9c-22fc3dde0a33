.field {
	@apply space-y-2;
}

.field label {
	@apply text-sm font-medium text-zinc-500;
}

.field.inline {
	@apply flex items-center gap-4 space-y-0;
}

.field.inline label {
	@apply w-36 text-sm font-medium text-zinc-500;
}

.input {
	@apply w-full rounded-md border border-zinc-200 px-3 py-2 text-sm transition-colors focus:border-zinc-400 focus:outline-none;
}
/*
.input[type='color'] {
	@apply h-9 w-9 rounded-full p-0.5;
} */

.input[type='color'] {
	border-radius: 100% !important;
	overflow: hidden;
	height: 2.25rem !important;
	width: 2.25rem !important;
	box-sizing: border-box !important;
	appearance: none !important;
	-moz-appearance: none !important;
	-webkit-appearance: none !important;
	background: none !important;
	border: 0 !important;
	cursor: pointer !important;
	padding: 0 !important;
}

.input[type='checkbox'] {
	@apply h-4 w-4 rounded border-zinc-300 p-2 text-zinc-900 focus:ring-zinc-900;
}

.select {
	@apply w-full rounded-md border border-zinc-200 px-3 py-2 text-sm transition-colors focus:border-zinc-400 focus:outline-none;
}

.textarea {
	@apply min-h-[8rem] w-full rounded-md border border-zinc-200 px-3 py-2 text-sm transition-colors focus:border-zinc-400 focus:outline-none;
}

.section-card {
	@apply space-y-6 rounded-lg border border-zinc-200 p-4;
}

.section-header {
	@apply flex items-center justify-between;
}

.section-title {
	@apply text-sm font-medium;
}

.section-controls {
	@apply flex items-center gap-2;
}

.button {
	@apply rounded-md px-4 py-2 text-sm font-medium transition-colors;
}

.button-primary {
	@apply bg-zinc-900 text-white hover:bg-zinc-800;
}

.button-secondary {
	@apply border border-zinc-200 text-zinc-600 hover:border-zinc-300 hover:bg-zinc-50 hover:text-zinc-900;
}

.button-danger {
	@apply border border-red-200 text-red-600 hover:bg-red-50;
}
