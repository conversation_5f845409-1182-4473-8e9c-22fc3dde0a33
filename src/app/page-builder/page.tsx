// import { getSpectaclePage } from '@/utils/api'
import dynamic from 'next/dynamic'
import './page.css'
// import Playground from './playground'

const Playground = dynamic(() => import('./playground'), { ssr: false })
// import { CustomPageSchema } from '../components/custom-page-components'

export default function Page() {
	// const pageData = await getSpectaclePage()
	// const spektaklExample: CustomPageSchema = {
	// 	constants: {
	// 		background: '#020617',
	// 		accentColors: ['#7B39EC', '#6E29DA'],
	// 		mutedTextColor: '#CBD5E1',
	// 		textColor: '#ffffff',
	// 		mutedBackground: '#080E20',
	// 		backgroundRing: '#1D1E2D',
	// 		displayFontFamily: 'termina, sans-serif',
	// 		defaultFontFamily:
	// 			'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"'
	// 	},
	// 	sections: [
	// 		{
	// 			type: 'hero',
	// 			props: {
	// 				background: 'https://dev.technischools.com/assets/link/65fda8cfe0a43eb6470998b0',
	// 				title: pageData.title,
	// 				description: pageData.description,
	// 				cta: 'Zapisz się',
	// 				ctaHref: '/',
	// 				ctaTextColor: '#ffffff',
	// 				imageUrl: 'https://dev.technischools.com/assets/link/65faeff1b21a230ae1003b72',
	// 				textShadow: true
	// 			}
	// 		},
	// 		{
	// 			type: 'rich-text',
	// 			props: {
	// 				title: 'Wydarzenie',
	// 				richTextProps: {
	// 					strongFontFamily: 'termina, sans-serif',
	// 					content: `<p> 						Spektakl <strong>GDZIE MNIE WIDZISZ...</strong> 						<br /> 						skupia uwagę na problemach młodzieży oraz ich emocjach. 					</p> 					<p>Samotność, odrzucenie, tęsknota, cierpienie, nadzieja, strach...</p> 					<p> 						Aktorzy skłaniają widza do refleksji poprzez emocje wyrażane ciałem zatopionym w tańcu i 						wzajemnych relacjach. 					</p> 					<p> 						<strong>To jak zobaczysz ten spektakl zależy tylko od Ciebie...</strong> 					</p> 					<br /> 					<p> 						<strong>Gdzie mnie widzisz...</strong> 					</p> 					<p> 						<strong>...w moim życiu?</strong> 					</p> 					<p> 						<strong>...w swoim życiu?</strong> 					</p> 					<p> 						<strong>Czy mnie widzisz?</strong> 					</p> 					<p> 						<strong>Gdzie mnie widzisz mamo... tato...?</strong> 					</p>`
	// 				}
	// 			}
	// 		},
	// 		{
	// 			type: 'cards',
	// 			props: {
	// 				fullWidth: true,
	// 				title: 'Twórcy',
	// 				cardsData: pageData.creators.map((c) => ({
	// 					title: c.fullName,
	// 					imgPath: c.img.path,
	// 					description: c.bio
	// 				})),
	// 				type: 'descriptive'
	// 			}
	// 		},
	// 		{
	// 			type: 'cards',
	// 			props: {
	// 				title: 'Zagrają',
	// 				cardsData: pageData.actors.map((c) => ({
	// 					title: c.fullName,
	// 					imgPath: c.img.path
	// 				})),
	// 				type: 'short'
	// 			}
	// 		},
	// 		{
	// 			type: 'date-time',
	// 			props: {
	// 				title: 'Data i miejsce',
	// 				titleProps: {
	// 					hideRectangle: true
	// 				},
	// 				background: '#080E20',
	// 				backgroundRounded: true,
	// 				backgroundRing: '#1D1E2D',
	// 				places: [
	// 					{
	// 						name: 'TEATR DRAMATYCZNY IM. GUSTAWA HOLOUBKA',
	// 						date: '24 KWIETNIA 2024',
	// 						time: '18:30',
	// 						address: 'PAŁAC KULTURY I NAUKI, PLAC DEFILAD 1, 00-901 WARSZAWA',
	// 						img: 'https://dev.technischools.com/assets/link/65fafbd1be3045f18d0b4fc0'
	// 					}
	// 				]
	// 			}
	// 		},
	// 		{
	// 			type: 'iframe',
	// 			props: {
	// 				title: 'Zapisz się',
	// 				typeformId: '01HSZXH1AEYS9QCZDQBRXKNX0T',
	// 				formName: 'smth'
	// 			}
	// 		},
	// 		{
	// 			type: 'logos',
	// 			props: {
	// 				logos: pageData.partners.map((partner) => ({
	// 					imgPath: partner.logo.path,
	// 					name: partner.name,
	// 					url: partner.url
	// 				})),
	// 				title: 'Wspierają nas'
	// 			}
	// 		},
	// 		{
	// 			type: 'links',
	// 			props: {
	// 				title: 'Kontakt',
	// 				titleProps: {
	// 					hideRectangle: true
	// 				},
	// 				background: '#080E20',
	// 				backgroundRing: '#1D1E2D',
	// 				links: [
	// 					{
	// 						title: 'Facebook',
	// 						linkText: 'Odwiedź nas na Facebooku',
	// 						url: 'https://www.facebook.com/technischools'
	// 					},
	// 					{
	// 						title: 'Instagram',
	// 						linkText: 'Odwiedź nas na Instagramie',
	// 						url: 'https://www.instagram.com/technischools'
	// 					},
	// 					{
	// 						title: 'LinkedIn',
	// 						linkText: 'Odwiedź nas na LinkedIn',
	// 						url: 'https://www.linkedin.com/company/technischools'
	// 					}
	// 				]
	// 			}
	// 		},
	// 		{
	// 			type: 'tabs',
	// 			props: {
	// 				title: 'Agenda',
	// 				tableProps: {
	// 					evenRowBackground: '#080E20'
	// 				},
	// 				tabs: [
	// 					{
	// 						title: 'Warszawa',
	// 						tableData: {
	// 							headers: ['Godzina', 'Sekcja'],
	// 							rows: [
	// 								['12:00', 'Warsztaty'],
	// 								['13:00', 'Przerwa'],
	// 								['14:00', 'Spektakl']
	// 							]
	// 						}
	// 					},
	// 					{ title: 'Lublin', htmlContent: '<div style={{ height: 300 }}>lublin</div>' }
	// 				]
	// 			}
	// 		}
	// 	]
	// }
	return (
		<div className="relative md:h-screen">
			<Playground />
		</div>
	)
}
//
