import { getTeachersPage, getStaffMembers, getCities } from '@/utils/api'
import { Member } from '../../Member'
import { notFound } from 'next/navigation'

export async function generateMetadata() {
	const { sectionData } = await getTeachersPage()
	return { title: sectionData.title }
}

export async function generateStaticParams() {
	const cities = await getCities()
	return cities.map((city) => ({ params: { type: 'teachers', city: city.city } }))
}

export default async function Staff({
	params: { type, city }
}: {
	params: {
		type: 'council' | 'teachers'
		city: string
	}
}) {
	const members = await getStaffMembers(type, city)
	const cities = await getCities()
	if (!cities.find((c) => c.city === city)) return notFound()

	return members.map((member) => <Member member={member} />)
}
