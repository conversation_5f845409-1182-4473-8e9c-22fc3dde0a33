import { getTeachersPage, getStaffMembers } from '@/utils/api'
import { Member } from '../Member'
import { notFound } from 'next/navigation'
import { STAFF_TYPES } from '@/utils/constants'

export async function generateMetadata() {
	const { sectionData } = await getTeachersPage()
	return { title: sectionData.title }
}

export default async function Staff({
	params: { type }
}: {
	params: {
		type: 'council' | 'teachers'
	}
}) {
	const members = await getStaffMembers(type)
	if (!STAFF_TYPES.includes(type)) return notFound()
	return members.map((member) => <Member member={member} />)
}
