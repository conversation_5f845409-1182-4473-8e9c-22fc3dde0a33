import { getTeachersPage, getCities, getCouncilPage } from '@/utils/api'
import Link from 'next/link'
import { StaffPage } from '@/types'
import { cn } from '@/utils/style'
import { STAFF_TYPES } from '@/utils/constants'

export async function generateMetadata() {
	const { sectionData } = await getTeachersPage()
	return { title: sectionData.title }
}

export async function generateStaticParams() {
	return STAFF_TYPES.map((type) => ({ params: { type } }))
}

export default async function StaffLayout({
	params: { type },
	children
}: {
	params: {
		type: 'council' | 'teachers'
		city?: string
	}
	children: React.ReactNode
}) {
	let sectionData: StaffPage
	if (type === 'council') {
		;({ sectionData } = await getCouncilPage())
	} else {
		;({ sectionData } = await getTeachersPage())
	}
	const cities = await getCities()
	return (
		<section className="container mx-auto flex min-h-screen flex-col gap-8 px-4 pb-12 lg:px-12">
			<div className="flex flex-col content-center align-middle">
				<h1 className="title text-gray-800">{sectionData.title}</h1>
				<ul className="mt-4 flex flex-col gap-8 md:flex-row">
					<Link
						className="h-fit w-fit flex-none border-b border-secondary border-opacity-0 text-center font-medium text-primary transition-all duration-300 hover:border-opacity-100 hover:text-secondary"
						href={`/staff/${type === 'council' ? 'teachers' : 'council'}`}
					>
						{sectionData.anchorText}
					</Link>
					{type === 'teachers' && (
						<>
							<span className="h-0.5 w-12 bg-gray-400 max-md:-mt-5 md:h-6 md:w-0.5" />
							<div className="flex w-full flex-wrap gap-8 max-md:-mt-5">
								<li>
									<Link
										href={`/staff/${type}`}
										className={cn(
											'mt-4 h-fit w-fit border-b border-secondary border-opacity-0 text-center font-medium text-primary transition-all duration-300 hover:border-opacity-100 hover:text-secondary'
										)}
									>
										Wszystkie miasta
									</Link>
								</li>
								{cities.map((city) => (
									<li key={city.city}>
										<Link
											href={`/staff/${type}/${city.city}`}
											className={cn(
												'mt-4 h-fit w-fit border-b border-secondary border-opacity-0 text-center font-medium text-primary transition-all duration-300 hover:border-opacity-100 hover:text-secondary'
											)}
										>
											{city.label}
										</Link>
									</li>
								))}
							</div>
						</>
					)}
				</ul>
			</div>

			<div className="flex h-full flex-wrap gap-8">{children}</div>
		</section>
	)
}
