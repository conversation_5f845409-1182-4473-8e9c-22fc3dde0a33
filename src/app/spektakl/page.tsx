import { cn } from '@/utils/style'
import Image from 'next/image'
import { HiArrowRight } from 'react-icons/hi2'

import './page.css'
import { getSpectaclePage } from '@/utils/api'
import { Metadata } from 'next'
import { getAssetPath } from '@/utils/assets'
import TypeformTracker from '../components/TypeformTracker'
export async function generateMetadata(): Promise<Metadata> {
	const data = await getSpectaclePage()
	return {
		title: data.title,
		description: data.description,
		keywords: data.keywords
	}
}

export default async function LubelskaWyspaPage() {
	const pageData = await getSpectaclePage()
	return (
		<div className="bg-slate-950 text-slate-100">
			{/* <div
				aria-hidden
				className="particles pointer-events-none fixed inset-0 z-20 -translate-y-32 [--particle-color-1:#D6A1A3] [--particle-color-2:#FFDE7C] [--particle-color-3:#72724C] max-md:-translate-y-64"
			>
				{[...Array(32)].map((_, i) => (
					<i key={i} className="particle" />
				))}
			</div> */}
			<div className="bg-[url(https://dev.technischools.com/assets/link/65fda8cfe0a43eb6470998b0)] bg-cover bg-center bg-no-repeat">
				<Section className="flex h-full items-center justify-between pt-32 md:flex-row md:pb-0">
					<div className="md:pb-24">
						<hgroup className="space-y-6 max-md:text-center">
							<h1 className="font-termina text-3xl font-semibold text-slate-50 [text-shadow:_0_1.5px_0_rgb(0_0_0_/_20%)] [text-wrap:balance] md:text-6xl">
								{pageData.title}
							</h1>
							<p className="text-slate-300 [text-wrap:balance] md:text-lg">
								{pageData.description}
							</p>
						</hgroup>
						<a
							href="#signup"
							className="btn-lg group mt-8 flex rounded-xl border-none bg-gradient-to-b from-violet-600 to-violet-700 py-3 text-white shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#********,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] [text-shadow:_0_1px_0_rgb(0_0_0_/_20%)] hover:brightness-105 active:scale-[0.98] max-md:mx-auto sm:text-lg"
						>
							Zapisz się
							<HiArrowRight className="ml-2 h-4 w-4 transition-transform [stroke-width:2px] group-hover:translate-x-1" />
						</a>
					</div>
					<div className="relative h-full overflow-hidden max-sm:hidden">
						<Image
							src={'https://dev.technischools.com/assets/link/65faeff1b21a230ae1003b72'}
							alt="Lubelska WySPA Techni"
							width={400}
							height={400}
							objectFit="cover"
						/>
					</div>
				</Section>
			</div>
			<Section id="wydarzenie">
				<SectionTitle>Wydarzenie</SectionTitle>
				<div className="max-w-prose space-y-5 text-left font-normal text-slate-300 sm:text-lg">
					<p>
						Spektakl{' '}
						<span className="font-termina font-medium text-slate-100">GDZIE MNIE WIDZISZ...</span>
						<br />
						skupia uwagę na problemach młodzieży oraz ich emocjach.
					</p>
					<p>Samotność, odrzucenie, tęsknota, cierpienie, nadzieja, strach...</p>
					<p>
						Aktorzy skłaniają widza do refleksji poprzez emocje wyrażane ciałem zatopionym w tańcu i
						wzajemnych relacjach.
					</p>
					<p className="font-termina font-medium text-slate-100">
						To jak zobaczysz ten spektakl zależy tylko od Ciebie...
					</p>
					<br />
					<p className="font-termina font-medium text-slate-100">Gdzie mnie widzisz...</p>
					<p className="font-termina font-medium text-slate-100">...w moim życiu?</p>
					<p className="font-termina font-medium text-slate-100">...w swoim życiu?</p>
					<p className="font-termina font-medium text-slate-100">Czy mnie widzisz?</p>
					<p className="font-termina font-medium text-slate-100">
						Gdzie mnie widzisz mamo... tato...?
					</p>
				</div>
			</Section>
			<Section id="creators">
				<SectionTitle>Twórcy</SectionTitle>
				<div className="flex h-full flex-wrap gap-8">
					{pageData.creators.map((creator) => (
						<div className="h-fit w-full rounded-3xl bg-slate-900/50 p-4 shadow-md ring-1 ring-white/10">
							<Image
								src={getAssetPath(creator.img.path)}
								alt={creator.fullName}
								width={240}
								height={240}
								className="aspect-square rounded-md object-cover object-top shadow-md ring-1 ring-white/10 max-sm:mb-4 max-sm:w-full sm:float-left sm:mr-4"
							/>
							<h4 className="mb-2 font-termina text-xl font-medium">{creator.fullName}</h4>
							<div className="space-y-3 leading-8">
								{creator.bio.split('\n').map((paragraph, i) => (
									<p key={i} className="text-slate-400">
										{paragraph}
									</p>
								))}
							</div>
						</div>
					))}
				</div>
			</Section>
			<Section id="actos">
				<SectionTitle>Zagrają</SectionTitle>
				<div className="flex h-full flex-wrap gap-8">
					{pageData.actors.map((actor) => (
						<div className="h-fit w-full rounded-3xl bg-slate-900/50 p-4 shadow-md ring-1 ring-white/10 sm:max-w-xs">
							<Image
								src={getAssetPath(actor.img.path)}
								alt={actor.fullName}
								width={160}
								height={160}
								className="mb-4 aspect-square w-full rounded-md object-cover object-top shadow-md ring-1 ring-black/5"
							/>
							<h4 className="font-termina text-xl font-medium">{actor.fullName}</h4>
						</div>
					))}
				</div>
				<div className="mt-12 flex flex-col items-center justify-center gap-8">
					<p className="font-termina text-lg font-medium">A także:</p>
					<div className="items-cetner flex flex-wrap justify-center gap-12">
						{pageData?.otherNames && (
							<p className="flex w-fit flex-col items-center text-center font-termina text-slate-400">
								{pageData.otherNames}
							</p>
						)}
					</div>
				</div>
			</Section>
			<div className="w-full rounded-3xl bg-slate-900/50 ring-1 ring-white/10">
				<Section id="where">
					<SectionTitle hideRectangle className="mb-12 h-fit">
						Data i miejsce
					</SectionTitle>
					<div className="flex items-center justify-around gap-8 text-slate-100 max-sm:flex-col">
						<div className="flex items-center justify-center gap-8 text-center max-sm:flex-col">
							<div className="relative aspect-square w-48 overflow-hidden rounded-2xl md:w-80">
								<Image
									alt={`Zdjęcie teatru`}
									src={'https://dev.technischools.com/assets/link/65fafbd1be3045f18d0b4fc0'}
									layout="fill"
									objectFit="cover"
								/>
							</div>
							<div className="flex flex-col items-center">
								<p className="mb-2">24 KWIETNIA 2024</p>
								<p className="mb-6">18:30</p>
								<h2 className="mb-6 max-w-xs font-termina text-3xl font-medium [text-wrap:balance]">
									TEATR DRAMATYCZNY IM. GUSTAWA HOLOUBKA
								</h2>
								<p className="max-w-xs [text-wrap:balance]">
									PAŁAC KULTURY I NAUKI, <br />
									PLAC DEFILAD 1, 00-901 WARSZAWA
								</p>
							</div>
						</div>
					</div>
				</Section>
			</div>

			<Section id="signup">
				<SectionTitle>Zapisz się</SectionTitle>
				<div
					className="mx-auto h-auto w-[800px] max-w-full rounded-3xl bg-slate-900/50 p-3 shadow-md ring-1 ring-white/10"
					data-tf-live="01HSZXH1AEYS9QCZDQBRXKNX0T"
				></div>
				<TypeformTracker formName="spektakl" />
			</Section>

			<Section>
				<SectionTitle>Wspierają nas</SectionTitle>
				<div className="grid grid-cols-2 gap-10 md:grid-cols-3 md:gap-x-28 md:gap-y-10 lg:gap-x-24 xl:gap-x-56 2xl:gap-x-96">
					{pageData.partners.map((partner, index) => (
						<a
							key={index}
							href={partner.url}
							target="_blank"
							rel="noreferrer"
							className="flex items-center justify-center"
						>
							<Image
								src={getAssetPath(partner.logo.path)}
								alt={partner.name}
								width={180}
								height={180}
							></Image>
						</a>
					))}
				</div>
				<div className="mt-12 flex flex-col items-center justify-center gap-8">
					<p className="font-termina text-lg">
						<span className="font-semibold">Wesprzyj nas</span>, dobrowolną darowizną na konto
						Fundacji i <span className="font-semibold"> zostań naszym Patronem:</span>
					</p>

					<p className="flex w-fit flex-col items-center text-center font-termina text-slate-300">
						Fundacja Techni Schools 83 1140 2004 0000 3702 8471 6418, mBank
						<br /> KRS: ********** NIP: **********
					</p>
				</div>
			</Section>
		</div>
	)
}

function Section({
	children,
	className,

	...props
}: React.HTMLAttributes<HTMLDivElement> & {
	children: React.ReactNode
	className?: string
}) {
	return (
		<section className={cn('container mx-auto px-4 py-12 lg:px-12', className)} {...props}>
			{children}
		</section>
	)
}

function SectionTitle({
	children,
	className,
	hideRectangle = false,
	...props
}: React.HTMLAttributes<HTMLDivElement> & {
	children: React.ReactNode
	className?: string
	hideRectangle?: boolean
}) {
	return (
		<div
			className={cn(
				'relative mb-8 flex h-20 w-full items-center text-slate-50 [text-shadow:_0_1.5px_0_rgb(0_0_0_/_20%)]',
				className
			)}
			{...props}
		>
			{!hideRectangle && (
				<div className="absolute left-[-38%] top-0 h-full w-full rounded-r-3xl bg-gradient-to-b from-violet-600 to-violet-700 shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#********,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] sm:left-[-35%] md:left-[-46%] xl:left-[-60%]"></div>
			)}
			<h2 className="relative z-[2] font-termina text-2xl font-semibold [text-shadow:_0_1.5px_0_rgb(0_0_0_/_20%)] sm:text-3xl lg:text-5xl">
				{children}
			</h2>
		</div>
	)
}
