.particle {
	width: 10px;
	height: 10px;
	border-radius: 50%;
	filter: blur(1px);
	margin: 20px;
	display: inline-block;
	-webkit-animation: particles 3s linear 2s 20;
	-moz-animation: particles 3s linear 2s 20;
	animation: particles 4s linear 2s 20;
	position: relative;
	background: var(--particle-color-1);
}

@keyframes particles {
	0% {
		-webkit-transform: translate3d(0, 0, 0) rotate(0deg) scale(0.6);
	}
	100% {
		-webkit-transform: translate3d(15px, 1200px, 0px) rotate(360deg) scale(0.6);
	}
}
.particles {
	--base-animation-speed: 6s;
}
.particles .particle:nth-child(3n) {
	-webkit-animation-duration: var(--base-animation-speed);
	-webkit-animation-iteration-count: 30;
	-webkit-transform-origin: right -45px;
	background: var(--particle-color-2);
}

.particles .particle:nth-child(3n + 1) {
	-webkit-animation-duration: calc(var(--base-animation-speed) * 1.5);
	-webkit-animation-iteration-count: 45;
	-webkit-transform-origin: right -30px;
	background: var(--particle-color-3);
}

.particles .particle:nth-child(3n + 2) {
	-webkit-animation-duration: calc(var(--base-animation-speed) * 2);
	-webkit-animation-iteration-count: 60;
	-webkit-transform-origin: right -15px;
	background: var(--particle-color-1);
}

.particles .particle:nth-child(7n) {
	opacity: 0.3;
	-webkit-animation-delay: 0s;
	-webkit-animation-timing-function: ease-in;
}
.particles .particle:nth-child(7n + 1) {
	opacity: 0.4;
	-webkit-animation-delay: 1s;
	-webkit-animation-timing-function: ease-out;
}
.particles .particle:nth-child(7n + 2) {
	opacity: 0.5;
	-webkit-animation-delay: 1.5s;
	-webkit-animation-timing-function: linear;
}
.particles .particle:nth-child(7n + 3) {
	opacity: 0.6;
	-webkit-animation-delay: 2s;
	-webkit-animation-timing-function: ease-in;
}
.particles .particle:nth-child(7n + 4) {
	opacity: 0.7;
	-webkit-animation-delay: 2.5s;
	-webkit-animation-timing-function: linear;
}
.particles .particle:nth-child(7n + 5) {
	opacity: 0.8;
	-webkit-animation-delay: 3s;
	-webkit-animation-timing-function: ease-out;
}
.particles .particle:nth-child(7n + 6) {
	opacity: 0.9;
	-webkit-animation-delay: 3.5s;
	-webkit-animation-timing-function: ease-in;
}
