'use client'

import Script from 'next/script'
import { useCountdown } from './use-count-down'
import { useEffect, useState } from 'react'

export const CountDown = ({ targetDate }: { targetDate: string | number | Date }) => {
	const [days, hours, minutes, seconds] = useCountdown(targetDate)
	if (days === 0 && hours === 0 && minutes === 0 && seconds === 0) {
		return null
	}
	function addZeroToSingleDigit(number: number) {
		return number < 10 ? `0${number}` : number
	}
	return (
		<div className="space-x-1 text-center font-rubik text-lg font-semibold tabular-nums sm:space-x-2.5 sm:text-4xl">
			<span className="text-primary">{addZeroToSingleDigit(days)}</span>
			<span className="sm:text-2xl">dni</span>
			<span className="text-primary">{addZeroToSingleDigit(hours)}</span>
			<span className="sm:text-2xl">godzin</span>
			<span className="text-primary">{addZeroToSingleDigit(minutes)}</span>
			<span className="sm:text-2xl">minut</span>
			<span className="text-primary">{addZeroToSingleDigit(seconds)}</span>
			<span className="sm:text-2xl">sekund</span>
		</div>
	)
}

export const CountDownForm = ({ targetDate }: { targetDate: string | number | Date }) => {
	const [currentTime, setCurrentTime] = useState(new Date())
	useEffect(() => {
		if (currentTime > new Date(targetDate)) {
			return
		}
		const interval = setInterval(() => {
			setCurrentTime(new Date())
			if (currentTime > new Date(targetDate)) {
				clearInterval(interval)
			}
		}, 1000)
		return () => clearInterval(interval)
	}, [])

	return true ? (
		<>
			<p className="mx-auto mb-4 text-center text-lg font-semibold sm:text-xl">
				Rejestracja zakończona.
			</p>
	
		</>
	) : (
		<>
			<div
				data-tf-widget="nYflU3uM"
				data-tf-opacity="100"
				data-tf-iframe-props="title=Lubelska WySPA Techni"
				data-tf-transitive-search-params
				data-tf-medium="snippet"
				className="mx-auto h-[700px] w-[800px] max-w-full rounded-xl bg-white p-3 shadow-md ring-1 ring-black/5"
			></div>
			<Script src="//embed.typeform.com/next/embed.js"></Script>
		</>
	)
}
