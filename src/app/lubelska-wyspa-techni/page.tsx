import { cn } from '@/utils/style'
import Image from 'next/image'
import { HiArrowRight, HiEnvelope } from 'react-icons/hi2'
import Logo from '../components/Logo'
import heroImg from './hero.png'
import partner2 from './partner2.png'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from './tabs'
import { CountDownForm } from './count-down'

export const metadata = {
	title: 'Lubelska WySPA Techni',
	description:
		'14 listopada w WSPA odbędzie się bezpłatna konferencja programistyczna dla dzieci i młodzieży Lubelska WySPA Techni.',
	keywords: [
		'14 listopada',
		'WSPA (Wyższa Szkoła Przedsiębiorczości i Administracji)',
		'konferencja programistyczna',
		'dzieci i młodzież',
		'Lubelska WySPA Techni',
		'bezpłatna',
		'programowanie',
		'edukacja',
		'technologia',
		'wydar<PERSON>ie',
		'nauka',
		'IT',
		'Lublin',
		'szkoła',
		'informatyka'
	]
}

const SPEAKERS = [
	{
		fullName: '<PERSON><PERSON><PERSON>',
		lecture:
			'Jak zostać hackerem? Jak włamać się na kamerkę internetową? Czy to w ogóle jest legalne?',
		bio: 'Doświadczony programista i etyczny hacker, który nie tylko posiada zaawansowane umiejętności techniczne. Jako założyciel Techni Schools, Mateusz skupia się na edukacji przyszłych talentów programistycznych. Partner w funduszu inwestycyjnym Techni Ventures, w którym aktywnie inwestuje w obiecujące startupy. Poza życiem zawodowym, Mateusz jest również lokalnym patriotą, co podkreśla',
		img: {
			path: 'https://dev.technischools.com/assets/link/652ce7ec53f52647cc06f391',
			alt: 'Mateusz Kozłowski'
		}
	},
	{
		fullName: 'Sławomir Sobótka',
		lecture:
			'Przyszłość jest teraz - jak zmienią się reguły gry kiedy skończysz edukację. Jak będzie wyglądać przyszłość?',
		bio: 'W ciągu 20 lat pracy w branży IT pełnił role programisty, architekta, lidera zespołu, mentora i trenera. Od 14 lat właściciel firmy szkoleniowo doradczej Bottega IT Minds zrzeszającej 70 ekspertów technicznych.',
		img: {
			path: 'https://dev.technischools.com/assets/link/6421a6ed81cd9cc67904a5f0',
			alt: 'Sławomir Sobótka'
		}
	},
	{
		fullName: 'Sebastian Mysakowski',
		lecture: 'From zero to hero - jeden kod, aplikacja na trzy platformy',
		bio: 'Senior Frontend Developer @Northmill Bank AB, podcaster, wykładowca na Akademii Leona Koźmińskiego, autor kursów w wydawnictwie Helion oraz No Fluff Jobs, mentor oraz prowadzący warsztaty. Od początku związany z JavaScriptem. Doświadczenie zawodowe zdobywał zaczynając od startupów kończąc na firmach z indeksu S&P 500.',
		img: {
			path: 'https://dev.technischools.com/assets/link/6526ae0c09a7a8e93f03fa50',
			alt: 'Sebastian Mysakowski'
		}
	},
	{
		fullName: 'Marek Golan',
		lecture: 'Jakie są różne ścieżki kariery w branży IT?',
		bio: 'Przedsiębiorca, pasjonat technologii. Ponad 17 lat komercyjnego doświadczenia w branży IT jako programista i właściciel firm programistycznych. Pracował z setkami klientów i dziesiątkami programistów. Obecnie prowadzi Software House Dogtronic. Miejsce, w którym tworzone są produkty dla małych, dużych i tych bardzo dużych. Od niedawna Prezes Stowarzyszenia ESTA Cluster, organizacji zrzeszającej firmy IT z siedzibą w Lublinie.',
		img: {
			path: 'https://dev.technischools.com/assets/link/6526ae0c09a7a8e93f03fa52',
			alt: 'Marek Golan'
		}
	},
	{
		fullName: 'Mikołaj Kosyra',
		lecture: 'Jakbym to rozegrał mając nagle 20 lat mniej?',
		bio: 'Doświadczony Scrum Master i Manager z bogatym zapleczem technicznym. W przeszłości pracował jako programista .NET (SQL, C#, Sharepoint), Team Lead, Scrum Master, Portfolio Manager, Support Manager, Project Manager i Product Owner. Zawsze po zwinnej stronie mocy :) \n W wolnym czasie gram z synami w gry planszowe, solowe RPG (Nintendo), CS2, MMORPG ze znajomymi lub oglądam seriale.',
		img: {
			path: 'https://dev.technischools.com/assets/link/652914c926ef74dfe109b810',
			alt: 'Mikołaj Kosyra'
		}
	},
	{
		fullName: 'Patryk Gałach',
		lecture: 'Czy mogę zostać twórcą gier w ciągu 48 godzin?',
		bio: 'Współzałożyciel Reality Unit oraz fundacji Lublin GameDev, zapalony twórca gier i do tego bloger gamedevowy. Organizator wielu warsztatów oraz game jamów. Posiada wieloletnie doświadczenie w różnych dziedzinach technologii takich jak: tworzenie gier, AR, VR, mobile, web oraz IoT. \n Uwielbia dzielić się tym doświadczeniem w ramach róznych wydarzeń rozpoczynając od małych spotkań, a końcąc na wielkich konferencjach. Jak sam określa - jedyną rzeczą lepszą od grania w gry jest ich tworzenie!',
		img: {
			path: 'https://dev.technischools.com/assets/link/652914c926ef74dfe109b811',
			alt: 'Patryk Gałach'
		}
	},
	{
		fullName: 'Mariusz Łobejko',
		lecture: 'Czy, kiedy i po co się specjalizować w konkretnej technologii?',
		bio: 'Head of Software Development \n Doświadczony konsultant IT (19 lat doświadczenia), głównie w obszarze (Automatyzacji sprzedaży, bankowości, Systemach enterprise, Systemach mobilnych, MedTech, Ecommerce)',
		img: {
			path: 'https://dev.technischools.com/assets/link/6526ae0c09a7a8e93f03fa51',
			alt: 'Mariusz Łobejko'
		}
	},
	{
		fullName: 'Julian Korgol',
		lecture: 'Jak w wieku 17 lat można zostać hackerem?',
		bio: 'Uczeń Techni Schools. Pasjonat Linuxa, nowych technologii i cyberbezpieczeństwa. Swoją przygodę zaczął w wieku 6/7 lat. Dzisiaj współpracuje z firmami z zakresu tworzenia dedykowanego oprogramowania, optymalizacji pewnych funkcjonalności, marketingu, doradztwa IT, rozwiązań chmurowych, DevOpsu, cyberbezpieczeństwa i wielu innych dziedzin informatyki.',
		img: {
			path: 'https://dev.technischools.com/assets/link/652ce7ec53f52647cc06f393',
			alt: 'Julian Korgol'
		}
	},
	{
		fullName: 'Jakub Leszcz',
		lecture: 'Programowanie IoT',
		bio: 'Programista języka Python. Głównie zajmuje się uczeniem maszynowym oraz szeroko pojętą sztuczną inteligencją. Absolwent UMCS. Doświadczenie zawodowe zdobywa w projektach badawczo rozwojowych. Poza sferą zawodową interesujęe się sportem, głównie piłką nożną oraz koszykówką.',
		img: {
			path: 'https://dev.technischools.com/assets/link/652ce985e9d8315656021070',
			alt: 'Jakub Leszcz'
		}
	},
	{
		fullName: 'Kacper Stefaniak',
		lecture: 'Sztuczna inteligencja',
		bio: 'Programista Machine Learning. Na co dzień rozwiazuje wyzwania zwiazane z analizą danych i tworzeniem innowacyjnych projektow B+R. Po godzinach fascynują go gry komputerowe i rynki finansowe.',
		img: {
			path: 'https://dev.technischools.com/assets/link/65366defc010333c350d3630',
			alt: 'Kacper Stefaniak'
		}
	},
	{
		fullName: 'Paweł Nowak',
		lecture: 'Sztuczna inteligencja',
		bio: 'Zawodowo prgramista w projektach komercyjnych opartych na AI, a także nauczyciel przedmiotów ścisłych. Absolwent UMCS. Doświadczenie zawodowe zdobył w licznych projektach B+R lub komercyjnych, gdzie wykorzystywał takie techniki jak np. analiza ludzkiego głosu, przetwarzanie języka naturalnego, prognozowanie w szeregach czasowych.',
		img: {
			path: 'https://dev.technischools.com/assets/link/64477cf251476ba2a709c8cf',
			alt: 'Paweł Nowak'
		}
	},
	{
		fullName: 'Albert Błaziak',
		lecture: 'Etyczne hackowanie',
		bio: 'Programista z szerokim zakresem zainteresowań, technologicznych i nie tylko. Doświadczenie zdobywał w projektach badawczo rozwojowych stosując najnowsze rozwiązania AI. Dziś jako Python Developer pracuje w szerokiej gamie projektów - od prostych aplikacji backendowych do zaawansowanych problemów wykorzystujących geolokalizację czy machine learning. Interesuje się także cyberbezpieczeństwem. Albert znany jest ze swojej otwartości na innych, co udowadniają prowadzone przez niego liczne zajęcia i warsztaty.',
		img: {
			path: 'https://dev.technischools.com/assets/link/652cea3dc14c089b650645a0',
			alt: 'Albert Błaziak'
		}
	},
	{
		fullName: 'Piotr Orłowski',
		lecture: 'Programowanie robotów',
		bio: 'Odpowiada za inżynierię oprogramowania w Empik.com. Buduje i rozwija zespoły, podnosi poziom organizacji, jakość techniczną i procesy wzmacniające niezawodność rozwiązań. Od ponad 25 lat związany z technologiami online, budował platformę dla portalu gazeta.pl, rozwijał serwisy internetowe merlin.pl, empik.com, agito.pl oraz allegro.pl i inne. \n Praca z ludźmi jest jego pasją, uwielbia obserwować i wspierać ich rozwój.',
		img: {
			path: 'https://dev.technischools.com/assets/link/652ce7ec53f52647cc06f392',
			alt: 'Piotr Orłowski'
		}
	},
	{
		fullName: 'Adam Kruk',
		lecture: 'Programowanie gier',
		bio: 'Unity developer pracujący przy grach AAA na komputery stacjonarne a od ponad roku pracuje przy grach mobilnych. Zajmuje się implementacją logik, UI, art, animacji oraz VFX czy SFX a także rozwiązań multiplayer przy użyciu Photon, Mirror czy połączenia z React. Pracuje dla największych polskich wydawców gier tj. PlayWay, Out Of the Ordinary. Współzałożyciel Workplays prężnie rozwijającego się job boardu dla ludzi z gamedevu',
		img: {
			path: 'https://dev.technischools.com/assets/link/646f36b18bf704a17809c884',
			alt: 'Adam Kruk'
		}
	},
	{
		fullName: 'Agata Baska',
		lecture: 'Empik - czyli jak połączyć omnichannel i zadbać o jakość',
		bio: 'Team Leaderka z 8-letnim doświadczeniem w dziedzinie testowania oprogramowania oraz pasją do odkrywania nowych narzędzi i technologii. Pracowała w wielu projektach w Polsce i za granicą. W codziennej pracy nie tylko tworzy strategie testowania, ale też aktywnie uczestniczy w rozwoju testów automatycznych używając takich narzędzi jak Selenide czy Playwright. Agata stawia na efektywną komunikacje, która stanowi dla niej fundament dobrze zorganizowanego zespołu.',
		img: {
			path: 'https://dev.technischools.com/assets/link/653bbca314d545b90d07efa0',
			alt: 'Agata Baska'
		}
	},
	{
		fullName: 'Małgorzata Dziubich',
		lecture: 'Empik - czyli jak połączyć omnichannel i zadbać o jakość',
		bio: 'Team Leaderka z 8-letnim doświadczeniem w obszarze rozwoju aplikacji mobilnych na platformy iOS i Android. Na co dzień decyduje o kierunkach rozwoju aplikacji Empik.com i wyznacza jej standardy. Stawia na jakość, począwszy od wyjątkowego designu, poprzez płynne interakcje, aż po stabilność aplikacji. Z pełnym zaangażowaniem dzieli się swoją wiedzą, doświadczeniem i dobrymi praktykami w zespole i poza nim. Gosia jest pasjonatką technologii, dzięki czemu tworzy aplikacje, które spełniają oczekiwania użytkowników i odpowiadają na najnowsze trendy.',
		img: {
			path: 'https://dev.technischools.com/assets/link/653bbc9cbd34a7bb9f0b7080',
			alt: 'Małgorzata Dziubich'
		}
	}
]

const EVENT_DATE = '2023-10-30T17:30:00'

export default function LubelskaWyspaPage() {
	return (
		<>
			<Section className="container mx-auto flex h-full items-center justify-between sm:items-center md:flex-row md:pb-24">
				<div>
					<hgroup className="space-y-6 max-md:text-center">
						<h1 className="font-rubik text-4xl font-semibold [text-shadow:_0_1.5px_0_rgb(0_0_0_/_20%)] [text-wrap:balance] md:text-7xl">
							<span className="text-lime-500">L</span>ubelska
							<br />
							<span className="text-red-600">W</span>ySPA <span className="text-primary">T</span>
							echni
						</h1>
						<p className="text-zinc-600 [text-wrap:balance] md:text-lg">
							Bezpłatne wydarzenie IT dla młodzieży
						</p>
					</hgroup>
					<a
						href="#signup"
						className="btn-lg group mt-8 flex rounded-xl border-none bg-gradient-to-b from-lime-300 to-lime-500 py-3 text-lime-950 shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] hover:brightness-105 active:scale-[0.98] max-md:mx-auto sm:text-lg"
					>
						Zapisz się
						<HiArrowRight className="ml-2 h-4 w-4 transition-transform [stroke-width:2px] group-hover:translate-x-1" />
					</a>
				</div>
				<div className="relative aspect-[4/5] w-[30vw] max-sm:hidden">
					<Image src={heroImg} alt="Lubelska WySPA Techni" layout="fill" objectFit="cover" />
				</div>
			</Section>
			<Section>
				<SectionTitle>Wydarzenie</SectionTitle>
				<div className="max-w-prose space-y-5 font-normal text-zinc-700 sm:text-lg">
					<p>
						Bezpłatne <span className="font-rubik font-medium text-zinc-950">wydarzenie IT</span>{' '}
						dla młodzieży organizowane przez Technikum Programistyczne Techni Schools, Wyższą Szkołę
						Przedsiębiorczości i Administracji oraz Miasto Lublin w ramach Europejskiej Stolicy
						Młodzieży.
					</p>
					<p>
						<span className="font-rubik font-medium text-zinc-950">Lubelska WySPA Techni</span> to
						wydarzenie poświęcone tematyce IT, programowaniu, sztucznej inteligencji oraz nowym
						technologiom. Skierowane do wszystkich, których ciekawi lub dopiero chcą poznać branżę
						IT.
					</p>
					<p>
						Interesują Cię nowe technologie?{' '}
						<a
							href="#signup"
							className="font-rubik font-medium text-primary underline underline-offset-2"
						>
							Zapisz się!
						</a>
					</p>
					<p>
						Wydarzenie jest <span className="font-rubik font-medium text-zinc-950">otwarte</span>{' '}
						dla wszystkich szkół oraz młodzieży.{' '}
						<span className="font-rubik font-medium text-zinc-950">Liczba miejsc ograniczona!</span>{' '}
						<br />W wydarzeniu mogą uczestniczyć klasy:
						<p className="font-rubik font-medium text-zinc-950">- podstawowe 7-8</p>
						<p className="font-rubik font-medium text-zinc-950">- średnie 1-5</p>
					</p>
					<p className="font-semibold">Zapisu na wydarzenie dokonuje nauczyciel.</p>
					<div>
						<p>Data: 14.11.2023</p>
						<p>Godzina: 9:00</p>
						<p>Miejsce: Wyższa Szkoła Przedsiębiorczości i Administracji w Lublinie</p>
					</div>
				</div>
			</Section>
			<div className="bg-stepper bg-cover bg-top bg-no-repeat">
				<Section>
					<SectionTitle>Agenda</SectionTitle>
					<Tabs defaultValue="aula1">
						<TabsList className="max-sm:w-full">
							<TabsTrigger className="max-sm:w-full" value="aula1">
								Aula I
							</TabsTrigger>
							<TabsTrigger className="max-sm:w-full" value="aula2">
								Aula II
							</TabsTrigger>
							<TabsTrigger className="max-sm:w-full" value="warsztaty">
								Warsztaty
							</TabsTrigger>
						</TabsList>
						<div className="mt-2 h-full rounded-lg border bg-white p-4 shadow-md ring-1 ring-black/5">
							<TabsContent
								value="aula1"
								className="grid-row-g grid h-fit w-fit grid-cols-[10rem_1fr] gap-y-4 sm:text-lg"
							>
								<span className="mr-2 font-rubik font-semibold tabular-nums">09:00 – 10:00</span>
								<span>ROZPOCZĘCIE + Od Turbo Pascala do Nowego Jorku</span>
								<span className="mr-2 font-rubik font-semibold tabular-nums">10:00 – 10:45</span>
								<span>
									Mateusz Kozłowski - Jak zostać hackerem? Jak włamać się na kamerkę internetową?
									Czy to w ogóle jest legalne?
								</span>
								<span className="mr-2 font-rubik font-semibold tabular-nums">11:00 – 11:45</span>
								<span>
									Sławomir Sobótka - Przyszłość jest teraz - jak zmienią się reguły gry kiedy
									skończysz edukację. Jak będzie wyglądać przyszłość? Ciężko powiedzieć ale
									teraźniejszość jest wystarczająco ciekawa.
								</span>
								<span className="mr-2 font-rubik font-semibold tabular-nums">12:00 – 12:45</span>
								<span>
									Sebastian Mysakowski - From zero to hero - jeden kod, aplikacja na trzy platformy
								</span>
								<span className="mr-2 font-rubik font-semibold tabular-nums">13:00 – 13:45</span>
								<span>Marek Golan - Jakie są różne ścieżki kariery w branży IT?</span>
							</TabsContent>
							<TabsContent
								className="grid-row-g grid h-fit w-fit grid-cols-[10rem_1fr] gap-y-4 sm:text-lg"
								value="aula2"
							>
								<span className="mr-2 font-rubik font-semibold tabular-nums">09:00 – 09:45</span>
								<span>ROZPOCZĘCIE + Empik - czyli jak połączyć omnichannel i zadbać o jakość</span>
								<span className="mr-2 font-rubik font-semibold tabular-nums">10:00 – 10:45</span>
								<span>Mikołaj Kosyra - Jakbym to rozegrał mając nagle 20 lat mniej?</span>
								<span className="mr-2 font-rubik font-semibold tabular-nums">11:00 – 11:45</span>
								<span>Patryk Gałach - Czy mogę zostać twórcą gier w ciągu 48 godzin?</span>
								<span className="mr-2 font-rubik font-semibold tabular-nums">12:00 – 12:45</span>
								<span>
									Mariusz Łobejko - Czy, kiedy i po co się specjalizować w konkretnej technologii?
								</span>
								<span className="mr-2 font-rubik font-semibold tabular-nums">13:00 – 13:45</span>
								<span>Julian Korgol - Jak w wieku 17 lat można zostać hackerem?</span>
							</TabsContent>
							<TabsContent
								className="grid-row-g grid h-fit w-fit grid-cols-[5rem_1fr] gap-y-4 sm:text-lg"
								value="warsztaty"
							>
								<span className="mr-2 font-rubik font-semibold">Sala I:</span>
								<span>Adam Kruk - Programowanie gier / Piotr Orłowski - Programowanie robotów</span>
								<span className="mr-2 font-rubik font-semibold">Sala II:</span>
								<span>Albert Błaziak - Etyczne hackowanie</span>
								<span className="mr-2 font-rubik font-semibold">Sala III:</span>
								<span>Paweł Nowak - Sztuczna Inteligencja</span>
								<span className="mr-2 font-rubik font-semibold">Sala IV:</span>
								<span>Jakub Leszcz - Programowanie IoT</span>
								<span className="mr-2 font-rubik font-semibold">Sala V:</span>
								<span>Kacper Stefaniak - Sztuczna Inteligencja</span>
							</TabsContent>
						</div>
					</Tabs>
				</Section>
			</div>
			<Section>
				<SectionTitle>Prelegenci</SectionTitle>
				<div className="flex h-full flex-wrap gap-8">
					{SPEAKERS.map((prelegent) => (
						<div className="h-fit w-full max-w-xl rounded-xl p-4 shadow-md ring-1 ring-black/5 md:h-96">
							<Image
								src={prelegent.img.path}
								alt={prelegent.img.alt}
								width={160}
								height={160}
								className="aspect-square rounded-md object-cover object-top shadow-md ring-1 ring-black/5 max-sm:mb-4 max-sm:w-full sm:float-left sm:mr-4"
							/>
							<h4 className="mb-0.5 font-rubik text-xl font-medium">{prelegent.fullName}</h4>
							<p className="mb-4 font-rubik text-primary">{prelegent.lecture}</p>
							<div className="space-y-3 leading-8">
								{prelegent.bio.split('\n').map((paragraph, i) => (
									<p key={i} className="text-zinc-700">
										{paragraph}
									</p>
								))}
							</div>
						</div>
					))}
				</div>
			</Section>
			<Section id="signup" className="bg-stepper bg-cover bg-center bg-no-repeat">
				<SectionTitle>Zapisz się!</SectionTitle>
				<CountDownForm targetDate={EVENT_DATE} />
			</Section>
			<div className="w-full bg-gradient-to-b from-red-700 to-red-800 shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)]">
				<Section className="flex items-center gap-8 max-sm:flex-col">
					<SectionTitle
						hideRectangle
						className="mb-0 h-fit [text-shadow:_0_1.5px_0_rgb(0_0_0_/_20%)] sm:h-fit"
					>
						Kontakt
					</SectionTitle>
					<div className="flex flex-col items-center gap-4">
						<a
							href="mailto:<EMAIL>"
							className="btn-lg mx-auto flex w-full rounded-xl border-none bg-gradient-to-b from-white to-zinc-100 py-3 text-zinc-950 hover:brightness-95 active:scale-[0.98] max-sm:w-full sm:text-lg"
						>
							<HiEnvelope className="mr-2 h-4 w-4" />
							<EMAIL>
						</a>
						<a
							href="https://technischools.com/regulamin-lubelska-wyspa-techni"
							className="btn-lg mx-auto flex w-full rounded-xl border-none bg-gradient-to-b from-white to-zinc-100 py-3 text-zinc-950 hover:brightness-95 active:scale-[0.98] max-sm:w-full sm:text-lg"
						>
							Regulamin
						</a>
					</div>
				</Section>
			</div>
			<Section>
				<SectionTitle hideRectangle className="h-fit text-inherit sm:h-fit">
					Organizatorzy
				</SectionTitle>
				<div className="flex items-center justify-between gap-8 py-8 max-md:flex-wrap max-sm:flex-col">
					<Image
						height={160}
						width={320}
						src={'https://dev.technischools.com/assets/link/653f938d9726d50599024a10'}
						alt="organizers 1"
					/>
					<Image
						height={100}
						width={160}
						src={'https://dev.technischools.com/assets/link/653f93f8677e195c960461a0'}
						alt="organizers 2"
					/>
					<div>
						<Image height={120} src={partner2} alt="organizers 3" />
					</div>
					<div className="w-48">
						<Logo />
					</div>
				</div>
			</Section>
			<Section>
				<SectionTitle hideRectangle className="h-fit text-inherit sm:h-fit">
					Partnerzy
				</SectionTitle>
				<div className="flex items-center justify-center gap-x-48 gap-y-12 py-8 max-md:flex-wrap max-sm:flex-col">
					<div>
						<Image
							height={160}
							width={160}
							src={'https://dev.technischools.com/assets/link/652cf4717d378d6aaf0b8300'}
							alt="partners 1"
						/>
					</div>
					<div>
						<Image
							height={160}
							width={160}
							src={'https://dev.technischools.com/assets/link/652cf4717d378d6aaf0b8301'}
							alt="partners 2"
						/>
					</div>
					<div>
						<Image
							height={160}
							width={160}
							src={'https://dev.technischools.com/assets/link/652cf4717d378d6aaf0b8302'}
							alt="partners 3"
						/>
					</div>
					<div>
						<Image
							height={160}
							width={160}
							src={'https://dev.technischools.com/assets/link/653f9f5fbd07ce11dd0c0900'}
							alt="partners 4"
						/>
					</div>
<div>
						<Image
							height={160}
							width={160}
							src={'https://dev.technischools.com/assets/link/6544b73bcf3ffd16c6088a50'}
							alt="partners 5"
						/>
					</div>
				</div>
			</Section>
		</>
	)
}

function Section({
	children,
	className,
	...props
}: React.HTMLAttributes<HTMLDivElement> & {
	children: React.ReactNode
	className?: string
}) {
	return (
		<section className={cn('container mx-auto px-4 py-12 lg:px-12', className)} {...props}>
			{children}
		</section>
	)
}

function SectionTitle({
	children,
	className,
	hideRectangle = false,
	...props
}: React.HTMLAttributes<HTMLDivElement> & {
	children: React.ReactNode
	hideRectangle?: boolean
	className?: string
}) {
	return (
		<div
			className={cn('relative mb-8 flex h-20 w-full items-center text-white', className)}
			{...props}
		>
			{!hideRectangle && (
				<div className="absolute -left-1/2 top-0 h-full w-full rounded-3xl bg-gradient-to-b from-red-700 to-red-800 shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] sm:-left-2/3"></div>
			)}
			<h2 className="relative z-[2] font-rubik text-2xl font-bold [text-shadow:_0_1.5px_0_rgb(0_0_0_/_20%)] sm:text-3xl lg:text-5xl">
				{children}
			</h2>
		</div>
	)
}
