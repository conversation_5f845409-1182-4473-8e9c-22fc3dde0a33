import { getAmountOfPosts, getBlogPage } from '@/utils/api'
import Masonry from '@components/Masonry'
import Link from 'next/link'

export default async function Blog({
	searchParams: { page }
}: {
	searchParams: { page?: string }
}) {
	const { pageData, posts } = await getBlogPage(Number(page ?? 1))
	const amountOfPosts = await getAmountOfPosts('article')
	return (
		<>
			{posts && posts.length !== 0 ? (
				<>
					<Masonry array={posts} />
					{posts.length < amountOfPosts && (
						<Link
							className="btn mx-auto"
							href={`/blog?page=${Number(page ?? 1) + 1}`}
							scroll={false}
						>
							<PERSON><PERSON><PERSON><PERSON> więcej
						</Link>
					)}
				</>
			) : (
				<div className="flex h-[40vh] w-full flex-col items-center justify-center gap-4 text-center">
					<h2 className="text-2xl font-medium text-gray-700">{pageData.noPostsTitle}</h2>
					<p className="text-gray-600">{pageData.noPostsSubtitle}</p>
				</div>
			)}
		</>
	)
}
