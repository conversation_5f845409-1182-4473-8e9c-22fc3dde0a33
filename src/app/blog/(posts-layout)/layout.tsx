import { getBlogLayout } from '@/utils/api'
import { Metadata } from 'next'

export async function generateMetadata(): Promise<Metadata> {
	const { title } = await getBlogLayout()
	return { title }
}

export default async function BlogLayout({ children }: { children: React.ReactNode }) {
	const { title } = await getBlogLayout()
	return (
		<section className="container mx-auto mb-10 grid gap-8 px-4 lg:px-12">
			<h1 className="title text-gray-800">{title}</h1>
			{children}
		</section>
	)
}
