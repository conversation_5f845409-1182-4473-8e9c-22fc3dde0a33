import { Post as PostType } from '@/types'
import { getCities, getCommonTexts } from '@/utils/api'
import { getAssetPath } from '@/utils/assets'
import { i18n } from '@/utils/i18n'
import { cn } from '@/utils/style'
import Image from 'next/image'
import Link from 'next/link'
import { HiMap, HiOutlineCalendarDays } from 'react-icons/hi2'

export const Post = async ({
	post: { type, img, title, date, description, slug, location },
	fixedSize
}: {
	post: PostType
	fixedSize?: boolean
}) => {
	const { readMoreText } = await getCommonTexts()
	const cities = await getCities()
	const locale = i18n.defaultLocale
	return (
		<article className="group mb-6 w-full break-inside-avoid-column">
			<Link
				href={`/${type === 'article' ? 'blog' : `announcements/${location}`}/${slug}`}
				className="grid gap-3"
			>
				<div className={cn('mb-1 overflow-hidden rounded-lg', fixedSize && 'h-80')}>
					<Image
						src={getAssetPath(img.path)}
						alt={slug}
						className="h-full w-full rounded-lg object-cover transition duration-500 ease-out group-hover:scale-105"
						width={500}
						height={(500 / img.width) * img.height}
					/>
				</div>
				<h2 className="text-2xl font-semibold text-gray-800 duration-300 hover:text-secondary">
					{title}
				</h2>
				<hr className="h-0.5 w-12 bg-gradient-to-r from-primary to-secondary transition-all delay-150 duration-300 ease-out group-hover:w-24" />
				<div className="flex items-center gap-2 text-xs text-gray-600 duration-300 hover:text-secondary">
					<HiOutlineCalendarDays size={16} />
					{new Date(date).toLocaleDateString(locale, {
						year: 'numeric',
						month: 'long',
						day: 'numeric'
					})}
				</div>
				{type === 'announcement' && (
					<div className="flex items-center gap-2 text-xs text-gray-600 duration-300 hover:text-secondary">
						<HiMap size={16} />
						<span>{cities.find((city) => city.city === location)?.label}</span>
					</div>
				)}
				<p className="line-clamp-2 text-clip text-gray-600">{description}</p>
				<p className="text-sm font-bold text-gray-800 underline underline-offset-4 duration-300 hover:text-secondary">
					{readMoreText}
				</p>
			</Link>
		</article>
	)
}
