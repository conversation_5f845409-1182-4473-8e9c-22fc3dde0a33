import { getCustomPage, getCustomPagesInfo } from '@/utils/api'
import htmr from 'htmr'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import TypeformTracker from '../components/TypeformTracker'
import CustomPageBuilder, { CustomPageSchema } from '../components/custom-page-components'
// import InfoModal from '../components/InfoModal'

export async function generateMetadata({
	params: { customPage }
}: {
	params: { customPage: string[] }
}): Promise<Metadata> {
	const data = await getCustomPage(customPage.join('/'))
	if (!data) return {}
	const { title, description, keywords } = data
	return { title, description, keywords }
}

export async function generateStaticParams() {
	const pages = await getCustomPagesInfo()
	return pages.map((page) => ({ params: { customPage: page.slug.split('/') } }))
}

export default async function Page({
	params: { customPage }
}: {
	params: { customPage: string[] }
}) {
	const data = await getCustomPage(customPage.join('/'))
	// const infoModalData = await getInfoModal()
	if (!data) return notFound()

	if (data.pageSchema) {
		return <CustomPageBuilder schema={JSON.parse(data.pageSchema) as CustomPageSchema} />
	}
	return (
		<section className="container mx-auto mb-8 flex min-h-screen flex-col gap-4 px-4 lg:px-12">
			<h1 className="title mb-4 w-full text-gray-800">{data.title}</h1>
			{data.page.map((item, index) => (
				<div className="content [&>p:first-of-type]:mt-8" key={index}>
					{htmr(item, {
						transform: {
							img: (node) => (
								<img
									alt={node.alt}
									src={
										node.src?.includes('storage/uploads')
											? `${process.env.NEXT_PUBLIC_ASSETS_URL}${node.src.split('/uploads')[1]}`
											: node.src
									}
								/>
							),
							a: (node) => (
								<a
									target={node.target}
									rel={node.target === '_blank' ? 'noopener noreferrer' : ''}
									href={
										node.href?.startsWith('/assets/link')
											? `${process.env.NEXT_PUBLIC_ASSETS_URL?.split('/storage/')[0]}${node.href}`
											: node.href
									}
								>
									{node.children}
								</a>
							)
						}
					})}
				</div>
			))}
			{data?.code && <div dangerouslySetInnerHTML={{ __html: data.code }}></div>}

			{data.trackForm && <TypeformTracker formName={customPage.join('/')} includeScript={false} />}
			{/* {data.disableInfoModal && <InfoModal data={infoModalData} />} */}
		</section>
	)
}
