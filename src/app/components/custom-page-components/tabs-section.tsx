'use client'
import { ScrollA<PERSON> } from '@radix-ui/react-scroll-area'
import { Section, SectionProps } from '.'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../tabs'
import { cn } from '@/utils/style'

export type Tab = {
	title: string
	content?: React.ReactNode
	htmlContent?: string
	tableData?: {
		headers: string[]
		rows: string[][]
	}
	isTable?: boolean
}
export type TabsSectionProps = React.HTMLAttributes<HTMLDivElement> &
	SectionProps & {
		tabs: Tab[]
		tabsBackground?: string
		tabsTriggerBackground?: string
		tabsTriggerTextColor?: string
		tabsContentBackground?: string
		tabsContentTextColor?: string
		tabsContentRing?: string
		tabsRing?: string
		tableProps?: AutoTableProps
		maxWidth?: boolean
	}

type AutoTableProps = {
	headerTextColor?: string
	bodyTextColor?: string
	evenRowBackground?: string
	borderColor?: string
}

export function TabsSection({
	tabs,
	tabsBackground,
	tabsTriggerBackground,
	tabsTriggerTextColor,
	tabsContentBackground,
	tabsContentTextColor,
	tabsContentRing,
	tabsRing,
	tableProps,
	constants,
	maxWidth,
	...props
}: TabsSectionProps) {
	tabsBackground = tabsBackground ?? constants?.mutedBackground
	tabsTriggerBackground = tabsTriggerBackground ?? constants?.background
	tabsTriggerTextColor = tabsTriggerTextColor ?? constants?.textColor
	tabsContentBackground = tabsContentBackground ?? constants?.background
	tabsContentTextColor = tabsContentTextColor ?? constants?.textColor
	tabsContentRing = tabsContentRing ?? constants?.backgroundRing
	tabsRing = tabsRing ?? constants?.backgroundRing

	if (tableProps) {
		tableProps.headerTextColor = tableProps?.headerTextColor ?? constants?.textColor ?? 'black'
		tableProps.bodyTextColor = tableProps?.bodyTextColor ?? constants?.textColor ?? 'black'
		tableProps.evenRowBackground =
			tableProps?.evenRowBackground ?? constants?.mutedBackground ?? 'white'
		tableProps.borderColor = tableProps?.borderColor ?? constants?.backgroundRing ?? 'black'
	}

	return (
		<Section constants={constants} {...props}>
			{!!tabs.length && (
				<Tabs defaultValue={tabs[0].title}>
					<div
						className="no-scrollbar relative h-9 max-w-xl overflow-x-scroll rounded-lg bg-[var(--tabs-background)] text-[var(--tabs-content-text-color)] ring-1 ring-[var(--tabs-ring)] lg:w-fit"
						style={
							{
								'--tabs-background': tabsBackground,
								'--tabs-content-text-color': tabsContentTextColor,
								'--tabs-ring': tabsRing
							} as React.CSSProperties
						}
					>
						<TabsList className="top-1/2 flex w-full flex-row justify-stretch bg-transparent max-lg:absolute max-lg:-translate-y-1/2">
							{tabs.map((tab) => (
								<TabsTrigger
									className="data-[state=active]:bg-[var(--tabs-trigger-active-bg)] max-sm:w-full"
									value={tab.title}
									style={
										{
											color: tabsTriggerTextColor,
											'--tabs-trigger-active-bg': tabsTriggerBackground
										} as React.CSSProperties
									}
									key={tab.title}
								>
									{tab.title}
								</TabsTrigger>
							))}
						</TabsList>
					</div>

					<div
						className="mt-2 h-full rounded-3xl bg-[var(--tabs-content-bg)] p-4 shadow-md ring-1 ring-[var(--tabs-content-ring)]"
						style={
							{
								'--tabs-content-bg': tabsContentBackground,
								'--tabs-content-ring': tabsContentRing
							} as React.CSSProperties
						}
					>
						{tabs.map((tab) => (
							<TabsContent value={tab.title} key={tab.title}>
								{!tab.isTable && (
									<div
										className={cn('prose prose-zinc', maxWidth && 'max-w-none')}
										dangerouslySetInnerHTML={{ __html: tab.htmlContent ?? '' }}
									/>
								)}
								{tab.isTable && tab.tableData ? (
									<AutoTable data={tab.tableData} {...tableProps} />
								) : undefined}
							</TabsContent>
						))}
					</div>
				</Tabs>
			)}
		</Section>
	)
}

export function AutoTable({
	data,
	headerTextColor,
	bodyTextColor,
	evenRowBackground,
	borderColor
}: AutoTableProps & {
	data: { headers?: string[]; rows?: string[][] }
}) {
	return (
		<Table>
			<TableHeader
				className="[&_tr]:border-b-[--border-color]"
				style={{ '--border-color': borderColor } as React.CSSProperties}
			>
				<TableRow>
					{data?.headers?.map((header) => (
						<TableHead
							className="font-semibold uppercase"
							style={{ color: headerTextColor }}
							key={header}
						>
							{header}
						</TableHead>
					))}
				</TableRow>
			</TableHeader>
			<TableBody>
				{data?.rows?.map((row, i) => (
					<TableRow
						key={row.join()}
						className="border-b-[--border-color] even:bg-[--even-row-bg]"
						style={
							{
								'--even-row-bg': evenRowBackground,
								'--border-color': borderColor
							} as React.CSSProperties
						}
					>
						{row.map((cell) => (
							<TableCell className="text-base" style={{ color: bodyTextColor }} key={cell}>
								{cell}
							</TableCell>
						))}
					</TableRow>
				))}
			</TableBody>
		</Table>
	)
}
