import { getAssetPath } from '@/utils/assets'
import { cn } from '@/utils/style'
import Image from 'next/image'
import { HiArrowRight } from 'react-icons/hi2'
import TypeformTracker from '../TypeformTracker'
import { TabsSection, TabsSectionProps } from './tabs-section'

export type Constants = {
	background?: string
	backgroundRing?: string
	mutedBackground?: string
	accentColors?: string[]
	displayFontFamily?: string
	defaultFontFamily?: string
	textColor?: string
	mutedTextColor?: string
}

export type ConstantsProps = {
	constants?: Constants
}

export type SectionProps = ConstantsProps & {
	children?: React.ReactNode
	id?: string
	className?: string
	background?: string
	backgroundClassName?: string
	title?: string
	titleProps?: Omit<SectionTitleProps, 'content'>
	backgroundRounded?: boolean
	backgroundRing?: string
}

export type SectionTitleProps = ConstantsProps & {
	className?: string
	hideRectangle?: boolean
	content?: string
	bgColor?: string[]
	textColor?: string
	fontFamily?: string
}
export type HeroSectionProps = ConstantsProps & {
	title: string
	titleProps?: {}
	description: string
	background: string
	imageUrl: string
	cta: string
	ctaTextColor: string
	ctaBgColor?: string[]
	titleColor?: string
	descriptionColor?: string
	textShadow: boolean
	fontFamily?: string
	ctaHref?: string
	imageWidth?: number
	imageHeight?: number
	imageOffsetX?: number
	imageOffsetY?: number
}
export type RichTextProps = ConstantsProps & {
	children?: React.ReactNode
	content?: string
	textColor?: string
	strongTextColor?: string
	strongFontFamily?: string
	strongFontWeight?: string | number
}
export type LogosSectionProps = React.HTMLAttributes<HTMLDivElement> &
	SectionProps & {
		logos: {
			imgPath: string
			url: string
			name: string
		}[]
	}

export type IFrameSectionProps = React.HTMLAttributes<HTMLDivElement> &
	SectionProps & {
		isFormActive?: boolean
		typeformId?: string
		htmlContent?: string
		formName?: string
		cardBackground?: string
		cardRing?: string
		hideCard?: boolean
	}
export type CardData = {
	name: string
	title: string
	description?: string
	imgPath: string
}

export type CardsSectionProps = React.HTMLAttributes<HTMLDivElement> &
	SectionProps & {
		type: 'short' | 'descriptive'
		fullWidth?: boolean
		cardBackground?: string
		cardRing?: string
		cardTextColor?: string
		cardTitleColor?: string
		cardsData: CardData[]
	}

export type Place = {
	date: string
	time: string
	name: string
	address: string
	img: string
}

export type DateTimeSectionProps = React.HTMLAttributes<HTMLDivElement> &
	SectionProps & {
		places: Place[]
		textColor?: string
	}
export type RichTextSectionProps = React.HTMLAttributes<HTMLDivElement> &
	SectionProps & {
		richTextProps: RichTextProps
	}

export type LinksSectionProps = React.HTMLAttributes<HTMLDivElement> &
	SectionProps & {
		linkBackgroundColor?: string[]
		linkTextColor?: string
		textColor?: string
		links: {
			title: string
			linkText: string
			url: string
		}[]
	}

export type Id = {
	id: string
}

export type CustomSectionProps =
	| { type: 'hero'; props: HeroSectionProps & Id }
	| { type: 'logos'; props: LogosSectionProps & Id }
	| { type: 'iframe'; props: IFrameSectionProps & Id }
	| { type: 'cards'; props: CardsSectionProps & Id }
	| { type: 'date-time'; props: DateTimeSectionProps & Id }
	| { type: 'section'; props: SectionProps & Id }
	| { type: 'rich-text'; props: RichTextSectionProps & Id }
	| { type: 'links'; props: LinksSectionProps & Id }
	| { type: 'tabs'; props: TabsSectionProps & Id }

export type CustomPageSchema = {
	constants?: Constants
	sections: CustomSectionProps[]
}

export const AVAILABLE_SECTIONS = [
	'hero',
	'logos',
	'iframe',
	'cards',
	'date-time',
	'rich-text',
	'links',
	'tabs'
] as const

export function Section({
	id,
	children,
	className,
	background,
	backgroundClassName,
	title,
	titleProps,
	backgroundRounded,
	backgroundRing,
	constants,
	...props
}: React.HTMLAttributes<HTMLDivElement> & SectionProps) {
	const section = (
		<section
			className={cn('container mx-auto px-4 py-12 lg:px-12', className)}
			{...props}
			style={{ background: background?.startsWith('http') ? `url(${background})` : background }}
			id={id}
		>
			{title && <SectionTitle content={title} {...titleProps} constants={constants} />}
			{children}
		</section>
	)

	if (background) {
		return (
			<div
				className={cn(
					'w-full ring-[var(--background-ring)]',
					backgroundRounded && 'rounded-3xl',
					backgroundRing && 'ring-1',
					backgroundClassName
				)}
				style={
					{
						background: background?.startsWith('http') ? `url(${background})` : background,
						'--background-ring': backgroundRing
					} as React.CSSProperties
				}
			>
				{section}
			</div>
		)
	}
	return section
}

export function SectionTitle({
	className,
	hideRectangle = false,
	content,
	bgColor,
	textColor,
	fontFamily,
	constants,
	...props
}: SectionTitleProps) {
	bgColor = bgColor ?? constants?.accentColors ?? ['transparent', 'transparent']
	textColor = textColor ?? constants?.textColor
	fontFamily = fontFamily ?? constants?.displayFontFamily
	return (
		<div
			className={cn(
				'relative mb-8 flex h-20 w-full items-center [text-shadow:_0_1.5px_0_rgb(0_0_0_/_20%)]',
				className
			)}
			{...props}
			style={{
				color: textColor
			}}
		>
			{!hideRectangle && (
				<div
					className="absolute left-[-15%] top-0 h-full w-full rounded-r-3xl bg-gradient-to-b shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] sm:left-[-35%] md:left-[-32%]"
					style={{
						backgroundImage: `linear-gradient(to bottom, ${bgColor[0]}, ${
							bgColor[1] ?? bgColor[0]
						})`
					}}
				></div>
			)}
			<h2
				className="relative z-[2] text-2xl font-semibold [text-shadow:_0_1.5px_0_rgb(0_0_0_/_20%)] sm:text-3xl lg:text-5xl"
				style={{
					fontFamily
				}}
			>
				{content}
			</h2>
		</div>
	)
}

export function HeroSection({
	title,
	description,
	background,
	imageUrl,
	cta,
	ctaTextColor,
	ctaBgColor,
	titleColor,
	descriptionColor,
	textShadow,
	fontFamily,
	ctaHref,
	constants,
	imageWidth,
	imageHeight,
	imageOffsetX,
	imageOffsetY
}: HeroSectionProps) {
	ctaBgColor = ctaBgColor ?? constants?.accentColors ?? ['transparent', 'transparent']
	titleColor = titleColor ?? constants?.textColor
	descriptionColor = descriptionColor ?? constants?.mutedTextColor
	fontFamily = fontFamily ?? constants?.displayFontFamily
	return (
		<div
			style={{
				background: background.startsWith('http') ? `url(${background})` : background,
				backgroundSize: 'cover',
				backgroundPosition: 'center',
				backgroundRepeat: 'no-repeat'
			}}
		>
			<Section
				constants={constants}
				className="flex h-full items-center justify-between pt-32 md:flex-row md:pb-0"
			>
				<div className="md:pb-24">
					<hgroup className="space-y-6 max-md:text-center">
						<h1
							className={cn(
								'text-3xl font-semibold [text-wrap:balance] md:text-7xl',
								textShadow && '[text-shadow:_0_1.5px_0_rgb(0_0_0_/_20%)]'
							)}
							style={{ color: titleColor, fontFamily }}
						>
							{title.split('\\n').map((line, index) => (
								<>
									<span key={index}>{line}</span>
									<br />
								</>
							))}
						</h1>
						<p
							className="text-slate-300 [text-wrap:balance] md:text-lg"
							style={{ color: descriptionColor }}
						>
							{description}
						</p>
					</hgroup>
					<a
						href={ctaHref}
						className="btn-lg group mt-8 flex rounded-xl border-none bg-gradient-to-b py-3 shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] [text-shadow:_0_1px_0_rgb(0_0_0_/_20%)] hover:brightness-105 active:scale-[0.98] max-md:mx-auto sm:text-lg"
						style={{
							backgroundImage: `linear-gradient(to bottom, ${ctaBgColor[0]}, ${
								ctaBgColor[1] ?? ctaBgColor[0]
							})`,
							color: ctaTextColor
						}}
					>
						{cta}
						<HiArrowRight className="ml-2 h-4 w-4 transition-transform [stroke-width:2px] group-hover:translate-x-1" />
					</a>
				</div>
				<div
					className="relative h-full overflow-hidden max-sm:hidden"
					style={{
						transform: `translate(${imageOffsetX ?? 0}px, ${imageOffsetY ?? 0}px)`
					}}
				>
					<Image
						src={imageUrl}
						alt={title + ' image'}
						objectFit="cover"
						width={imageWidth ?? 600}
						height={imageHeight ?? 600}
					/>
				</div>
			</Section>
		</div>
	)
}

export function RichText({
	children,
	content,
	textColor,
	strongTextColor,
	strongFontFamily,
	strongFontWeight = 600,
	constants
}: RichTextProps) {
	textColor = textColor ?? constants?.textColor
	strongFontFamily = strongFontFamily ?? constants?.displayFontFamily

	return (
		<div
			className="prose prose-zinc max-w-2xl space-y-5 text-left font-normal sm:text-lg [&_strong]:font-[var(--strong-font-weight)] [&_strong]:text-[var(--strong-text-color)] [&_strong]:[font-family:var(--strong-font-family)]"
			style={
				{
					color: textColor,
					'--strong-text-color': strongTextColor,
					'--strong-font-family': strongFontFamily,
					'--strong-font-weight': strongFontWeight
				} as React.CSSProperties
			}
			{...(content && !children
				? { dangerouslySetInnerHTML: { __html: content } }
				: {
						children
				  })}
		></div>
	)
}

export function LogosSection({ logos, constants, ...props }: LogosSectionProps) {
	return (
		<Section constants={constants} {...props}>
			<div className="grid grid-cols-2 gap-10 md:grid-cols-3 md:gap-x-28 md:gap-y-10 lg:gap-x-24 xl:gap-x-56 2xl:gap-x-96">
				{logos.map((logo, index) => (
					<a
						key={index}
						href={logo.url}
						target="_blank"
						rel="noreferrer"
						className="flex items-center justify-center"
					>
						<Image
							src={logo.imgPath.startsWith('http') ? logo.imgPath : getAssetPath(logo.imgPath)}
							alt={logo.name}
							width={180}
							height={180}
						></Image>
					</a>
				))}
			</div>
		</Section>
	)
}

export function IFrameSection({
	isFormActive,
	typeformId,
	formName,
	cardBackground,
	cardRing,
	constants,
	htmlContent,
	hideCard,
	...props
}: IFrameSectionProps) {
	cardBackground = cardBackground ?? constants?.mutedBackground
	cardRing = cardRing ?? constants?.backgroundRing
	return (
		<Section constants={constants} {...props}>
			<div
				className={cn(
					'mx-auto h-auto min-h-[300px] w-[800px] max-w-full p-3',
					!hideCard &&
						'rounded-3xl bg-[var(--card-background)] shadow-md ring-1 ring-[var(--card-ring)]'
				)}
				style={
					{
						'--card-background': cardBackground,
						'--card-ring': cardRing
					} as React.CSSProperties
				}
				data-tf-live={typeformId}
				dangerouslySetInnerHTML={!isFormActive && htmlContent ? { __html: htmlContent } : undefined}
			></div>
			{formName && typeformId && isFormActive && (
				<TypeformTracker formName={formName} includeScript />
			)}
		</Section>
	)
}

export function CardsSection({
	fullWidth,
	type,
	cardBackground,
	cardRing,
	cardsData,
	constants,
	cardTextColor,
	cardTitleColor,
	...props
}: CardsSectionProps) {
	const hasDescription = type === 'descriptive'
	cardBackground = cardBackground ?? constants?.mutedBackground
	cardTextColor = cardTextColor ?? constants?.textColor
	cardRing = cardRing ?? constants?.backgroundRing
	return (
		<Section constants={constants} {...props}>
			<div className="flex h-full flex-wrap gap-8">
				{cardsData.map((cardData) => (
					<div
						key={cardData.title}
						className={cn(
							'h-fit w-full rounded-3xl bg-[var(--card-background)] p-4 shadow-md ring-1 ring-[var(--card-ring)]',
							!hasDescription && 'sm:max-w-xs',
							!fullWidth && 'max-w-xl'
						)}
						style={
							{
								'--card-background': cardBackground,
								'--card-ring': cardRing
							} as React.CSSProperties
						}
					>
						<Image
							src={
								cardData.imgPath.startsWith('http')
									? cardData.imgPath
									: getAssetPath(cardData.imgPath)
							}
							alt={cardData.title}
							width={hasDescription ? 240 : 160}
							height={hasDescription ? 240 : 160}
							className={cn(
								'aspect-square rounded-md object-cover object-top shadow-md ring-1 ring-white/10',
								hasDescription ? 'max-sm:mb-4 max-sm:w-full sm:float-left sm:mr-4' : 'mb-4 w-full'
							)}
						/>
						<h4 className="font-termina text-xl font-medium" style={{ color: cardTextColor }}>
							{cardData.name}
						</h4>
						<h4
							className={cn(
								'font-termina font-medium',
								cardData.name ? 'mt-2 text-base' : 'text-xl'
							)}
							style={{ color: cardTitleColor }}
						>
							{cardData.title}
						</h4>
						{hasDescription && (
							<div className="mt-2 space-y-3 leading-8">
								{cardData?.description?.split('\n').map((paragraph, i) => (
									<p key={i} style={{ color: cardTextColor }}>
										{paragraph}
									</p>
								))}
							</div>
						)}
					</div>
				))}
			</div>
		</Section>
	)
}

export function DateTimeSection({ places, textColor, constants, ...props }: DateTimeSectionProps) {
	return (
		<Section constants={constants} {...props}>
			<div
				className={cn(
					'grid grid-cols-1 place-content-center gap-8 text-[var(--text-color)]',
					places.length % 2 === 0
						? 'md:grid-cols-2 lg:grid-cols-4'
						: 'md:grid-cols-1 lg:grid-cols-3',
					places.length === 2 && 'md:grid-cols-2 lg:grid-cols-2',
					places.length === 1 && 'md:grid-cols-1 lg:grid-cols-1'
				)}
				style={{ '--text-color': textColor } as React.CSSProperties}
			>
				{places.map((place, index) => (
					<div
						className={cn(
							'flex items-center justify-center gap-8 text-center max-sm:flex-col',
							places.length >= 2 && 'flex-col'
						)}
						key={index}
					>
						<div className="relative aspect-square w-48 overflow-hidden rounded-2xl md:w-80">
							<Image alt={`Zdjęcie teatru`} src={place.img} layout="fill" objectFit="cover" />
						</div>
						<div className="flex flex-col items-center">
							<h2 className="mb-5 max-w-xs font-termina text-3xl font-medium [text-wrap:balance]">
								{place.name}
							</h2>
							<p className="mb-1.5">{place.date}</p>
							<p className="mb-5">{place.time}</p>
							<p className="max-w-xs [text-wrap:balance]">{place.address}</p>
						</div>
					</div>
				))}
			</div>
		</Section>
	)
}

export function RichTextSection({
	children,
	richTextProps,
	constants,
	...props
}: RichTextSectionProps) {
	return (
		<Section constants={constants} {...props}>
			<RichText {...richTextProps}>{children}</RichText>
		</Section>
	)
}

export function LinksSection({
	linkBackgroundColor,
	linkTextColor,
	textColor,
	links,
	constants,
	...props
}: LinksSectionProps) {
	linkBackgroundColor = linkBackgroundColor ??
		constants?.accentColors ?? ['transparent', 'transparent']
	linkTextColor = linkTextColor ?? constants?.textColor
	textColor = textColor ?? constants?.textColor
	return (
		<Section constants={constants} {...props}>
			<div className="flex items-center justify-between gap-8 max-xl:flex-col">
				{links.map((link, index) => (
					<div
						className="flex w-full flex-col items-center justify-center gap-x-8 gap-y-2 md:flex-row"
						key={index}
					>
						<span className="font-rubik text-lg font-medium" style={{ color: textColor }}>
							{link.title}
						</span>
						<a
							key={index}
							href={link.url}
							className="btn-lg group flex rounded-xl border-none py-3 shadow-[inset_0_1px_0_#ffffff5d,inset_0_-1px_0_#00000015,0_1px_3px_0_rgb(0_0_0_/_0.1),_0_1px_2px_-1px_rgb(0_0_0_/_0.1)] [text-shadow:_0_1px_0_rgb(0_0_0_/_20%)] hover:brightness-105 active:scale-[0.98] max-md:mx-auto max-md:w-full sm:text-lg"
							aria-label={`Visit ${link.linkText}`}
							style={{
								backgroundImage: `linear-gradient(to bottom, ${linkBackgroundColor![0]}, ${
									linkBackgroundColor![1] ?? linkBackgroundColor![0]
								})`,
								color: linkTextColor
							}}
						>
							{link.linkText}
						</a>
					</div>
				))}
			</div>
		</Section>
	)
}

export default function CustomPageBuilder({ schema }: { schema: CustomPageSchema }) {
	return (
		<div
			style={{
				fontFamily: schema?.constants?.defaultFontFamily,
				color: schema?.constants?.textColor,
				background: schema?.constants?.background
			}}
		>
			{schema.sections.map((section, index) => {
				switch (section.type) {
					case 'hero':
						return <HeroSection key={index} {...section.props} constants={schema.constants} />
					case 'section':
						return <Section key={index} {...section.props} constants={schema.constants} />
					case 'cards':
						return <CardsSection key={index} {...section.props} constants={schema.constants} />
					case 'date-time':
						return <DateTimeSection key={index} {...section.props} constants={schema.constants} />
					case 'logos':
						return <LogosSection key={index} {...section.props} constants={schema.constants} />
					case 'iframe':
						return <IFrameSection key={index} {...section.props} constants={schema.constants} />
					case 'rich-text':
						return <RichTextSection key={index} {...section.props} constants={schema.constants} />
					case 'links':
						return <LinksSection key={index} {...section.props} constants={schema.constants} />
					case 'tabs':
						return <TabsSection key={index} {...section.props} constants={schema.constants} />
					default:
						return null
				}
			})}
		</div>
	)
}
