import { type Operation, applyPatch } from 'fast-json-patch'
import { useReducer } from 'react'
import {
	AVAILABLE_SECTIONS,
	CardsSectionProps,
	Constants,
	CustomPageSchema,
	CustomSectionProps,
	DateTimeSectionProps,
	LinksSectionProps,
	LogosSectionProps,
	RichTextSectionProps
} from '.'
import { TabsSectionProps } from './tabs-section'

interface SchemaState {
	schema: CustomPageSchema
	history: CustomPageSchema[]
	historyIndex: number
	initialSchema: CustomPageSchema
}

export type CustomPageSchemaReducerAction =
	| {
			type: 'SET_CONSTANT'
			key: keyof Constants
			payload: string | string[]
	  }
	| {
			type: 'ADD_SECTION'
			payload: CustomSectionProps
	  }
	| {
			type: 'REMOVE_SECTION'
			payload: {
				index: number
			}
	  }
	| {
			type: 'MOVE_SECTION_UP'
			payload: {
				index: number
			}
	  }
	| {
			type: 'MOVE_SECTION_DOWN'
			payload: {
				index: number
			}
	  }
	| {
			type: 'SET_SECTION_PROP'
			index: number
			key: string
			payload: unknown
	  }
	| {
			type: 'SET_SECTION_TITLE_PROP'
			index: number
			key: string
			payload: unknown
	  }
	| {
			type: 'SET_TABS_SECTION_TABLE_PROPS'
			index: number
			key: string
			payload: unknown
	  }
	| {
			type: 'SET_RICH_TEXT_SECTION_PROP'
			index: number
			key: string
			payload: unknown
	  }
	| {
			type: 'ADD_EMPTY_SECTION'
			section: (typeof AVAILABLE_SECTIONS)[number]
	  }
	| {
			type: 'ADD_LOGO'
			index: number
	  }
	| {
			type: 'REMOVE_LOGO'
			index: number
			logoIndex: number
	  }
	| {
			type: 'SET_LOGO_PROP'
			index: number
			logoIndex: number
			key: string
			payload: unknown
	  }
	| {
			type: 'ADD_CARD'
			index: number
	  }
	| {
			type: 'REMOVE_CARD'
			index: number
			cardIndex: number
	  }
	| {
			type: 'SET_CARD_PROP'
			index: number
			cardIndex: number
			key: string
			payload: unknown
	  }
	| {
			type: 'ADD_PLACE'
			index: number
	  }
	| {
			type: 'REMOVE_PLACE'
			index: number
			placeIndex: number
	  }
	| {
			type: 'SET_PLACE_PROP'
			index: number
			placeIndex: number
			key: string
			payload: unknown
	  }
	| {
			type: 'ADD_LINK'
			index: number
	  }
	| {
			type: 'REMOVE_LINK'
			index: number
			linkIndex: number
	  }
	| {
			type: 'SET_LINK_PROP'
			index: number
			linkIndex: number
			key: string
			payload: unknown
	  }
	| {
			type: 'ADD_TAB'
			index: number
	  }
	| {
			type: 'REMOVE_TAB'
			index: number
			tabIndex: number
	  }
	| {
			type: 'SET_TAB_PROP'
			index: number
			tabIndex: number
			key: string
			payload: unknown
	  }
	| {
			type: 'REORDER_SECTIONS'
			payload: CustomSectionProps[]
	  }
	| {
			type: 'SET_SCHEMA'
			payload: CustomPageSchema
	  }
	| {
			type: 'APPLY_OPERATIONS'
			payload: Operation[]
	  }
	| {
			type: 'UNDO'
	  }
	| {
			type: 'REDO'
	  }

const limitHistory = (history: CustomPageSchema[], maxSize: number) => {
	return history.slice(0, maxSize)
}

const isTextAction = (action: CustomPageSchemaReducerAction) => {
	return action.type.includes('SET_') && 'payload' in action && typeof action.payload === 'string'
}

let pendingHistoryUpdate: NodeJS.Timeout | null = null

const customPageSchemaReducer = (
	state: SchemaState,
	action: CustomPageSchemaReducerAction
): SchemaState => {
	console.log('dispatch')
	if (action.type === 'UNDO' || action.type === 'REDO') {
		switch (action.type) {
			case 'UNDO': {
				if (state.historyIndex >= state.history.length) {
					return {
						...state,
						schema: state.initialSchema,
						history: state.history,
						historyIndex: state.history.length,
						initialSchema: state.initialSchema
					}
				}

				const newIndex = state.historyIndex + 1
				return {
					...state,
					schema: state.history[newIndex] ?? state.initialSchema,
					history: state.history,
					historyIndex: newIndex,
					initialSchema: state.initialSchema
				}
			}

			case 'REDO': {
				if (state.historyIndex <= 0) return state

				const newIndex = state.historyIndex - 1

				return {
					...state,
					schema: state.history[newIndex],
					history: state.history,
					historyIndex: newIndex,
					initialSchema: state.initialSchema
				}
			}
		}
	}
	const newState = (() => {
		switch (action.type) {
			case 'SET_SCHEMA': {
				const newHistory = [action.payload, ...state.history]
				return {
					...state,
					schema: action.payload,
					history: limitHistory(newHistory, 10),
					historyIndex: 0
				}
			}
			case 'SET_CONSTANT':
				return {
					...state,
					schema: {
						...state.schema,
						constants: { ...state.schema.constants, [action.key]: action.payload }
					}
				}
			case 'ADD_SECTION':
				return {
					...state,
					schema: { ...state.schema, sections: [...state.schema.sections, action.payload] }
				}
			case 'REMOVE_SECTION':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.filter((_, index) => index !== action.payload.index)
					}
				}
			case 'REORDER_SECTIONS':
				return { ...state, schema: { ...state.schema, sections: action.payload } }
			case 'SET_SECTION_PROP':
				const newSchema = {
					...state.schema,
					sections: state.schema.sections.map((section, index) =>
						index === action.index
							? {
									...section,
									props: {
										...section.props,
										[action.key]: action.payload
									}
							  }
							: section
					)
				} as CustomPageSchema

				return { ...state, schema: newSchema }

			case 'SET_SECTION_TITLE_PROP':
				if (state.schema.sections[action.index].type === 'hero') return state
				else {
					return {
						...state,
						schema: {
							...state.schema,
							sections: state.schema.sections.map((section, index) =>
								index === action.index
									? {
											...section,
											props: {
												...section.props,
												titleProps: {
													...section.props.titleProps,
													[action.key]: action.payload
												}
											}
									  }
									: section
							)
						} as CustomPageSchema
					}
				}
			case 'SET_TABS_SECTION_TABLE_PROPS':
				if (state.schema.sections[action.index].type === 'tabs') {
					return {
						...state,
						schema: {
							...state.schema,
							sections: state.schema.sections.map((section, index) =>
								index === action.index
									? {
											...section,
											props: {
												...section.props,
												tableProps: {
													...(section.props as TabsSectionProps).tableProps,
													[action.key]: action.payload
												}
											}
									  }
									: section
							)
						} as CustomPageSchema
					}
				}
				return state

			case 'SET_RICH_TEXT_SECTION_PROP':
				if (state.schema.sections[action.index].type === 'rich-text') {
					return {
						...state,
						schema: {
							...state.schema,
							sections: state.schema.sections.map((section, index) =>
								index === action.index
									? {
											...section,
											props: {
												...section.props,
												richTextProps: {
													...(section.props as RichTextSectionProps).richTextProps,
													[action.key]: action.payload
												}
											}
									  }
									: section
							)
						} as CustomPageSchema
					}
				}
				return state
			case 'ADD_EMPTY_SECTION':
				let newSection: CustomSectionProps
				switch (action.section) {
					case 'hero':
						newSection = {
							type: 'hero',
							props: {
								id: crypto.randomUUID(),
								background: '',
								title: 'Hero',
								description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
								cta: 'Get started',
								ctaTextColor: '',
								ctaHref: '/',
								imageUrl: '',
								textShadow: false,
								imageWidth: 400,
								imageHeight: 400,
								imageOffsetX: 0,
								imageOffsetY: 0
							}
						}
						break
					case 'logos':
						newSection = {
							type: 'logos',
							props: {
								id: crypto.randomUUID(),
								title: `Logos #${state.schema.sections.filter((s) => s.type === 'logos').length}`,
								logos: []
							}
						}
						break
					case 'iframe':
						newSection = {
							type: 'iframe',
							props: {
								id: crypto.randomUUID(),

								title: `IFrame #${state.schema.sections.filter((s) => s.type === 'iframe').length}`,
								htmlContent: '',
								hideCard: false,
								isFormActive: false,
								typeformId: '',
								formName: ''
							}
						}
						break
					case 'cards':
						newSection = {
							type: 'cards',
							props: {
								id: crypto.randomUUID(),

								title: `Cards #${state.schema.sections.filter((s) => s.type === 'cards').length}`,
								cardsData: [],
								type: 'short',
								fullWidth: false,
								cardBackground: '',
								cardRing: '',
								cardTextColor: ''
							}
						}
						break
					case 'date-time':
						newSection = {
							type: 'date-time',
							props: {
								id: crypto.randomUUID(),

								title: `Date Time #${
									state.schema.sections.filter((s) => s.type === 'date-time').length
								}`,
								places: []
							}
						}
						break
					case 'rich-text':
						newSection = {
							type: 'rich-text',
							props: {
								id: crypto.randomUUID(),

								title: `Rich Text #${
									state.schema.sections.filter((s) => s.type === 'rich-text').length
								}`,
								content: '',
								richTextProps: {
									textColor: '',
									strongTextColor: '',
									strongFontFamily: '',
									strongFontWeight: 600
								}
							}
						}
						break
					case 'links':
						newSection = {
							type: 'links',
							props: {
								id: crypto.randomUUID(),

								title: `Links #${state.schema.sections.filter((s) => s.type === 'links').length}`,
								links: [],
								linkBackgroundColor: [],
								linkTextColor: '',
								textColor: ''
							}
						}
						break
					case 'tabs':
						newSection = {
							type: 'tabs',
							props: {
								id: crypto.randomUUID(),

								title: `Tabs #${state.schema.sections.filter((s) => s.type === 'tabs').length}`,
								tabs: [
									{
										title: 'Tab 1',
										isTable: false,
										htmlContent: 'Tab 1 content'
									}
								]
							}
						}
						break
					default:
						newSection = {
							type: 'section',
							props: {
								id: crypto.randomUUID()
							}
						}
				}
				return {
					...state,
					schema: { ...state.schema, sections: [...state.schema.sections, newSection] }
				}
			case 'ADD_LOGO':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											logos: [
												...((section.props as LogosSectionProps).logos ?? []),
												{
													imgPath: '',
													name: '',
													url: ''
												}
											]
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'REMOVE_LOGO':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											logos: (section.props as LogosSectionProps).logos.filter(
												(_, i) => i !== action.logoIndex
											)
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'SET_LOGO_PROP':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											logos: (section.props as LogosSectionProps).logos.map((logo, i) =>
												i === action.logoIndex
													? {
															...logo,
															[action.key]: action.payload
													  }
													: logo
											)
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'ADD_CARD':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											cardsData: [
												...((section.props as CardsSectionProps).cardsData ?? []),
												{
													title: '',
													description: '',
													imgPath: ''
												}
											]
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'REMOVE_CARD':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											cardsData: (section.props as CardsSectionProps).cardsData.filter(
												(_, i) => i !== action.cardIndex
											)
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'SET_CARD_PROP':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											cardsData: (section.props as CardsSectionProps).cardsData.map((card, i) =>
												i === action.cardIndex
													? {
															...card,
															[action.key]: action.payload
													  }
													: card
											)
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'ADD_PLACE':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											places: [
												...((section.props as DateTimeSectionProps).places ?? []),
												{
													date: '',
													time: '',
													name: '',
													address: '',
													img: ''
												}
											]
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'REMOVE_PLACE':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											places: (section.props as DateTimeSectionProps).places.filter(
												(_, i) => i !== action.placeIndex
											)
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'SET_PLACE_PROP':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											places: (section.props as DateTimeSectionProps).places.map((place, i) =>
												i === action.placeIndex
													? {
															...place,
															[action.key]: action.payload
													  }
													: place
											)
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'ADD_LINK':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											links: [
												...((section.props as LinksSectionProps).links ?? []),
												{
													title: '',
													linkText: '',
													url: ''
												}
											]
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'REMOVE_LINK':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											links: (section.props as LinksSectionProps).links.filter(
												(_, i) => i !== action.linkIndex
											)
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'SET_LINK_PROP':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											links: (section.props as LinksSectionProps).links.map((link, i) =>
												i === action.linkIndex
													? {
															...link,
															[action.key]: action.payload
													  }
													: link
											)
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'ADD_TAB':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											tabs: [
												...((section.props as TabsSectionProps).tabs ?? []),
												{
													title: '',
													content: ''
												}
											]
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'REMOVE_TAB':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											tabs: (section.props as TabsSectionProps).tabs.filter(
												(_, i) => i !== action.tabIndex
											)
										}
								  }
								: section
						)
					} as CustomPageSchema
				}
			case 'SET_TAB_PROP':
				return {
					...state,
					schema: {
						...state.schema,
						sections: state.schema.sections.map((section, index) =>
							index === action.index
								? {
										...section,
										props: {
											...section.props,
											tabs: (section.props as TabsSectionProps).tabs.map((tab, i) =>
												i === action.tabIndex
													? {
															...tab,
															[action.key]: action.payload
													  }
													: tab
											)
										}
								  }
								: section
						)
					} as CustomPageSchema
				}

			case 'APPLY_OPERATIONS': {
				try {
					const newSchema = applyPatch(
						JSON.parse(JSON.stringify(state.schema)),
						action.payload
					).newDocument

					const newHistory = [newSchema, ...state.history]
					return {
						...state,
						schema: newSchema,
						history: limitHistory(newHistory, 10),
						historyIndex: 0,
						initialSchema: state.initialSchema
					}
				} catch (error) {
					console.error('Error applying patch:', error)
					return state
				}
			}

			case 'MOVE_SECTION_UP': {
				if (action.payload.index === 0) return state
				const newSections = [...state.schema.sections]
				const temp = newSections[action.payload.index - 1]
				newSections[action.payload.index - 1] = newSections[action.payload.index]
				newSections[action.payload.index] = temp
				return { ...state, schema: { ...state.schema, sections: newSections } }
			}

			case 'MOVE_SECTION_DOWN': {
				if (action.payload.index === state.schema.sections.length - 1) return state
				const newSections = [...state.schema.sections]
				const temp = newSections[action.payload.index + 1]
				newSections[action.payload.index + 1] = newSections[action.payload.index]
				newSections[action.payload.index] = temp
				return { ...state, schema: { ...state.schema, sections: newSections } }
			}

			default:
				return state
		}
	})()

	if (action.type === 'SET_SCHEMA') {
		return newState
	}
	const isDifferent = JSON.stringify(newState.schema) !== JSON.stringify(state.schema)

	if (isDifferent) {
		const isNotDuplicateOfLast =
			state.history.length === 0 ||
			JSON.stringify(newState.schema) !== JSON.stringify(state.history[0])

		if (isNotDuplicateOfLast) {
			if (isTextAction(action)) {
				if (pendingHistoryUpdate) clearTimeout(pendingHistoryUpdate)

				pendingHistoryUpdate = setTimeout(() => {
					newState.history = limitHistory([newState.schema, ...state.history], 10)
					pendingHistoryUpdate = null
				}, 1000)

				return newState
			}

			return {
				...newState,
				history: limitHistory([newState.schema, ...state.history], 10),
				historyIndex: 0
			}
		}
	}

	return newState
}

export function useCustomPageSchemaReducer(initialSchema: CustomPageSchema) {
	const [state, dispatch] = useReducer<React.Reducer<SchemaState, CustomPageSchemaReducerAction>>(
		customPageSchemaReducer,
		{
			schema: initialSchema,
			initialSchema: JSON.parse(JSON.stringify(initialSchema)),
			history: [],
			historyIndex: 0
		}
	)

	return [
		state.schema,
		dispatch,
		{
			canUndo: state.historyIndex <= state.history.length - 1,
			canRedo: state.historyIndex > 0,
			undo: () => dispatch({ type: 'UNDO' }),
			redo: () => dispatch({ type: 'REDO' }),
			reset: () => dispatch({ type: 'SET_SCHEMA', payload: initialSchema })
		}
	] as const
}
