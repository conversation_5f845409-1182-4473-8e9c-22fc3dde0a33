'use client'
import { cn } from '@/utils/style'
import { useEffect, useState } from 'react'
import { HiChevronUp } from 'react-icons/hi2'

export const ScrollUp = () => {
	const [isVisible, setIsVisible] = useState(false)
	const scrollToTop = () => {
		window.scrollTo({
			top: 0,
			behavior: 'smooth'
		})
	}
	useEffect(() => {
		const toggleVisibility = () => {
			if (window.pageYOffset > 300) {
				setIsVisible(true)
			} else {
				setIsVisible(false)
			}
		}
		window.addEventListener('scroll', toggleVisibility)
		return () => window.removeEventListener('scroll', toggleVisibility)
	}, [])
	return (
		<button
			aria-label="Przewiń do góry"
			className={cn(
				'fixed bottom-4 right-4 z-30 rounded-full bg-white p-3 text-primary shadow-lg transition-all duration-300 hover:scale-110 hover:bg-secondary hover:text-white hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2',
				isVisible ? 'scale-100 opacity-100' : 'pointer-events-none scale-0 opacity-0'
			)}
			onClick={scrollToTop}
		>
			<HiChevronUp size={24} />
		</button>
	)
}
