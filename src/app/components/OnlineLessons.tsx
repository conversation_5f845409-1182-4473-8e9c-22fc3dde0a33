import { getOnlineLessons } from '@/utils/api'

export default async function OnlineLessons() {
	const { title, description, buttonText, url } = await getOnlineLessons()
	return (
		<section
			className="container mx-auto grid scroll-mt-20 gap-12 px-4 py-32 lg:grid-cols-2 lg:place-content-center lg:px-12 2xl:min-h-fit"
			aria-labelledby="onlineLessonsTitle"
		>
			<div className="grid gap-8">
				<h2 id="onlineLessonsTitle" className="title mb-8 tracking-tight text-primary">
					{title}
				</h2>
				{description.map((paragraph, index) => (
					<p key={index} className="text-lg leading-relaxed text-gray-600">
						{paragraph}
					</p>
				))}
				<a
					rel="noreferrer"
					href={url}
					target="blank"
					className="group w-fit mt-6 inline-flex items-center gap-3 rounded-xl bg-gradient-to-r from-primary to-primary/90 px-8 py-4 font-semibold text-white shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl max-sm:w-full"
				>
					<span>{buttonText}</span>
					<svg className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
					</svg>
				</a>
			</div>
			<div className="group relative mt-12 md:mt-0 lg:ml-auto lg:max-w-lg">
				<div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-primary/20 to-secondary/20 opacity-0 blur transition-all duration-300 group-hover:opacity-100"></div>
				<iframe
					className="relative aspect-video w-full rounded-2xl object-cover shadow-2xl transition-all duration-300 group-hover:scale-105 md:h-80"
					title={title + ' video'}
					srcDoc={`
        <style>
            body, .full {
                width: 100%;
                height: 100%;
                margin: 0;
                position: absolute;
                display: flex;
                justify-content: center;
                object-fit: cover;
            }
        </style>
        <a
            href='${url}?autoplay=1'
            class='full'
        >
            <img
                src='https://vumbnail.com/${url.split('/').at(-1)}.jpg'
                class='full'
            />
            <svg
                version='1.1'
                viewBox='0 0 68 48'
                width='68px'
                style='position: relative;'
            >
                <path d='M66.52,7.74c-0.78-2.93-2.49-5.41-5.42-6.19C55.79,.13,34,0,34,0S12.21,.13,6.9,1.55 C3.97,2.33,2.27,4.81,1.48,7.74C0.06,13.05,0,24,0,24s0.06,10.95,1.48,16.26c0.78,2.93,2.49,5.41,5.42,6.19 C12.21,47.87,34,48,34,48s21.79-0.13,27.1-1.55c2.93-0.78,4.64-3.26,5.42-6.19C67.94,34.95,68,24,68,24S67.94,13.05,66.52,7.74z' fill='#f00'></path>
                <path d='M 45,24 27,14 27,34' fill='#fff'></path>
            </svg>
        </a>
    `}
					allowFullScreen
				></iframe>
			</div>
		</section>
	)
}
