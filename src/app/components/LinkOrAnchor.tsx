import Link from 'next/link'
import { HiArrowTopRightOnSquare } from 'react-icons/hi2'

interface LinkOrAnchorProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
	href: string
	children: React.ReactNode
	type: 'internal' | 'external' | 'anchor'
}

export const LinkOrAnchor = ({ href, children, type, ...props }: LinkOrAnchorProps) => {
	const isAnchor = href.includes('#') || type === 'anchor'
	const replaceFirstSlash = href.charAt(0) === '/' && href.substring(1)
	const hrefWithLocale = isAnchor ? `/${replaceFirstSlash}` : href
	const Component = isAnchor || type === 'external' ? 'a' : Link
	if (type === 'external' && !href.includes('http')) {
		href = `https://${href}`
	}
	return (
		<Component
			target={type === 'external' ? '_blank' : '_self'}
			href={type === 'external' ? href : hrefWithLocale}
			{...props}
		>
			{children}
			{type === 'external' && (
				<HiArrowTopRightOnSquare className="ml-2 inline-block h-4 w-4 font-medium" />
			)}
		</Component>
	)
}
