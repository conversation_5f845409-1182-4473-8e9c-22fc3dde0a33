import { getContactSection } from '@/utils/api'
import {
	FaFacebook,
	FaGithub,
	FaInstagram,
	FaLinkedinIn,
	FaMedium,
	FaTiktok,
	FaTwitter,
	FaYoutube
} from 'react-icons/fa'
import { <PERSON><PERSON><PERSON>, <PERSON>Envelope } from 'react-icons/hi2'

export default async function Contact() {
	const data = await getContactSection()
	const getSocialIcon = (name: string) => {
		switch (name) {
			case 'facebook':
				return <FaFacebook className="h-6 w-auto text-primary transition-all duration-300 group-hover:scale-110 group-hover:text-white" />
			case 'youtube':
				return <FaYoutube className="h-6 w-auto text-primary transition-all duration-300 group-hover:scale-110 group-hover:text-white" />
			case 'linkedin':
				return (
					<FaLinkedinIn className="h-6 w-auto text-primary transition-all duration-300 group-hover:scale-110 group-hover:text-white" />
				)
			case 'instagram':
				return <FaInstagram className="h-6 w-auto text-primary transition-all duration-300 group-hover:scale-110 group-hover:text-white" />
			case 'twitter':
				return <FaTwitter className="h-6 w-auto text-primary transition-all duration-300 group-hover:scale-110 group-hover:text-white" />
			case 'github':
				return <FaGithub className="h-6 w-auto text-primary transition-all duration-300 group-hover:scale-110 group-hover:text-white" />
			case 'medium':
				return <FaMedium className="h-6 w-auto text-primary transition-all duration-300 group-hover:scale-110 group-hover:text-white" />
			case 'tiktok':
				return <FaTiktok className="h-6 w-auto text-primary transition-all duration-300 group-hover:scale-110 group-hover:text-white" />

			default:
				return null
		}
	}
	return (
		<section
			className="container mx-auto mt-16 flex scroll-mt-20 items-center justify-between gap-12 px-4 py-16 max-lg:flex-col lg:px-12"
			aria-label="Contact"
		>
			<div className="flex flex-wrap items-center justify-center gap-6">
				{data.socials.map((social) => (
					<a
						key={social.type}
						href={social.url}
						rel="noreferrer"
						target="blank"
						className="group bg-gray-50 p-4 transition-all duration-300 hover:bg-primary hover:scale-110 hover:shadow-lg overflow-hidden focus-config rounded-xl"
						aria-label={social.type}
					>
						{getSocialIcon(social.type)}
					</a>
				))}
			</div>
			<div className="flex flex-col items-center justify-between gap-12 sm:w-auto md:flex-row">
				<div className="flex w-full flex-col items-center gap-8 md:flex-row xl:justify-around">
					<p className="text-lg font-medium text-primary">{data.callText}</p>
					<a
						href={`tel:${data.phone}`}
						className="group flex items-center gap-3 rounded-xl bg-gradient-to-r from-primary to-primary/90 px-6 py-4 font-semibold text-white shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl max-md:w-full"
						aria-label={`Call ${data.phone}`}
					>
						<div className="grid h-5 w-5 place-items-center">
							<HiPhone className="min-w-full transition-transform duration-300 group-hover:scale-110" />
						</div>
						{data.phone}
					</a>
				</div>
				<div className="flex w-full flex-col items-center gap-8 md:flex-row md:justify-between">
					<p className="text-lg font-medium text-primary">{data.emailText}</p>
					<a
						href={`mailto:${data.email}`}
						className="group flex items-center gap-3 rounded-xl bg-gradient-to-r from-secondary to-secondary/90 px-6 py-4 font-semibold text-white shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl max-md:w-full"
						aria-label={`Email ${data.email}`}
					>
						<div className="mr-1 grid h-5 w-5 place-items-center">
							<HiEnvelope className="min-w-full transition-transform duration-300 group-hover:scale-110" />
						</div>
						{data.email}
					</a>
				</div>
			</div>
		</section>
	)
}
