import { getFAQDescription } from '@/utils/api'
import Link from 'next/link'

export default async function FAQ() {
	const data = await getFAQDescription()
	return (
		<section
			className="container mx-auto flex scroll-mt-20 flex-col items-center justify-between px-4 py-12 sm:flex-row md:px-12 lg:h-auto lg:py-20 2xl:min-h-fit 2xl:py-16"
			aria-label="FAQ Section"
		>
			<p className="mb-10 text-center leading-relaxed text-gray-600 sm:mb-0 sm:text-left md:pr-8 md:text-lg md:leading-relaxed">
				{data.description}
			</p>
			<Link className="btn-md max-sm:w-full transition-all duration-200 hover:scale-105" href="/faq">
				FAQ
			</Link>
		</section>
	)
}
