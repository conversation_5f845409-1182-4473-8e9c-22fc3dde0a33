import { getAboutSchool } from '@/utils/api'
import { Post } from '../blog/Post'

export default async function AboutSchool() {
	const { sectionData, posts } = await getAboutSchool()
	return (
		<section
			className="container mx-auto grid gap-6 px-4 py-12 lg:px-12"
			aria-labelledby="aboutSchoolsTitle"
		>
			<h2 id="aboutSchoolsTitle" className="title text-primary">
				{sectionData.title}
			</h2>
			<p className="mb-4 text-gray-500">{sectionData.description}</p>
			<div className="grid w-full grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-3">
				{posts.map((post) => (
					<Post key={post.slug} post={post} fixedSize />
				))}
			</div>
		</section>
	)
}
