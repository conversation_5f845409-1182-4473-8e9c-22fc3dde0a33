import Image from 'next/image'
import { getPromises } from '@/utils/api'
import { getAssetPath } from '@/utils/assets'

export default async function Promises() {
	const data = await getPromises()
	return (
		<section
			className="container mx-auto w-full scroll-mt-20 place-items-center px-4 py-12 lg:min-h-fit lg:px-12"
			aria-labelledby="promisesOfTechniTitle"
		>
			<div className="grid h-full w-full gap-8">
				<h2 id="promisesOfTechniTitle" className="title text-primary">
					{data.title}
				</h2>
				<div className="relative h-[56vh] w-full md:h-[30rem]">
					<Image
						src={getAssetPath(data.img[0].path)}
						alt="PromisesOfTechni"
						className="hidden md:block"
						fill
					/>
					<Image
						src={getAssetPath(data.img[1].path)}
						alt="PromisesOfTechni"
						className="block md:hidden"
						fill
					/>
				</div>
			</div>
		</section>
	)
}
