import Image from 'next/image'
import Link from 'next/link'
import { getProgrammingBenefits } from '@/utils/api'
import { getAssetPath } from '@/utils/assets'

export default async function TechniBenefits() {
	const data = await getProgrammingBenefits()
	return (
		<section
			className="container mx-auto grid scroll-mt-20 place-items-center justify-center gap-12 px-4 py-32 sm:grid-cols-2 lg:px-12 2xl:min-h-fit"
			aria-labelledby="techniBenefitsTitle"
		>
			<div className="grid gap-8">
				<h2 id="techniBenefitsTitle" className="title tracking-tight text-primary">
					{data.title}
				</h2>
				<p className="text-lg leading-relaxed text-gray-600">{data.description}</p>
				<div className="grid gap-6">
					<h3 className="text-xl font-bold tracking-tight text-gray-800">{data.subtitle}</h3>
					<ul className="grid gap-4">
						{data.benefits.map((benefit, index) => (
							<li key={index} className="group flex items-start gap-4 rounded-xl bg-gradient-to-r from-gray-50/50 to-white/50 p-4 text-lg font-medium text-gray-700 transition-all duration-300">
								<span className="mt-2 h-3 w-3 flex-shrink-0 rounded-full bg-gradient-to-r from-primary to-secondary shadow-sm transition-all duration-300 group-hover:scale-110 group-hover:shadow-lg"></span>
								<span className="transition-colors duration-300 group-hover:text-gray-900">{benefit}</span>
							</li>
						))}
					</ul>
				</div>
				<Link
					href="/curriculum"
					className="group mt-6 w-fit inline-flex items-center gap-3 rounded-xl bg-gradient-to-r from-primary to-primary/90 px-8 py-4 font-semibold text-white shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl max-sm:w-full"
				>
					<span>{data.buttonText}</span>
					<svg className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
					</svg>
				</Link>
			</div>
			<div className="group relative">
				<Image
					src={getAssetPath(data.img.path)}
					alt="benefits"
					height={440}
					width={440}
					className="relative rounded-2xl transition-all duration-300 group-hover:scale-105"
				/>
			</div>
		</section>
	)
}
