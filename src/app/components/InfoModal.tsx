'use client'
import { Modal } from '@/types'
import { Dialog, Transition } from '@headlessui/react'
import { cn } from '@/utils/style'
import { Fragment, useEffect, useState } from 'react'
import { HiInformationCircle, HiPaperAirplane, HiXMark } from 'react-icons/hi2'

export default function InfoModal({ data }: { data: Modal }) {
	const [city, setCity] = useState<string>('Warszawa')
	const [phonenumber, setPhonenumber] = useState<string>('')
	const [accepted, setAccepted] = useState<boolean>(false)
	const [isLoading, setIsLoading] = useState<boolean>(false)
	const [success, setSuccess] = useState<boolean>(false)
	const [isOpen, setIsOpen] = useState(false)
	const [neverOpened, setNeverOpened] = useState(true)
	const reachedPercentage = usePercentageScrolled(data.positionPercentage || 80)
	const [customMessage, setCustomMessage] = useState<string>('')
	useEffect(() => {
		const handleScroll = () => {
			if (reachedPercentage && neverOpened) {
				openModal()
			}
		}
		window.addEventListener('scroll', handleScroll)
		return () => window.removeEventListener('scroll', handleScroll)
	}, [neverOpened, reachedPercentage])

	function closeModal() {
		setIsOpen(false)
		if (neverOpened) {
			setNeverOpened(false)
		}
		setSuccess(false)
	}

	function openModal() {
		setIsOpen(true)
	}

	function usePercentageScrolled(percentage = 80) {
		const [percentageScrolled, setPercentageScrolled] = useState(false)
		useEffect(() => {
			const handleScroll = () => {
				const scrollable = document.documentElement.scrollHeight - window.innerHeight
				const scrolled = window.scrollY
				const scrollPercent = (scrolled / scrollable) * 100
				if (scrollPercent > percentage) {
					setPercentageScrolled(true)
				} else {
					setPercentageScrolled(false)
				}
			}
			window.addEventListener('scroll', handleScroll)
			return () => window.removeEventListener('scroll', handleScroll)
		}, [])
		return percentageScrolled
	}

	async function handleForm(event: React.FormEvent<HTMLFormElement>) {
		event.preventDefault()
		if (!accepted) return
		setIsLoading(true)
		const response = await fetch('/api/popup', {
			method: 'POST',
			body: JSON.stringify({ city, phonenumber }),
			headers: {
				'Content-Type': 'application/json'
			}
		})
		setIsLoading(false)
		if (response.ok) {
			window.dataLayer.push({ enhanced_conversion_data: { phone_number: phonenumber } })
			setSuccess(true)
			setTimeout(() => {
				closeModal()
				setPhonenumber('')
			}, 2000)
		}
		if (response.status === 429) {
			setIsLoading(false)
			setSuccess(false)
			setCustomMessage('Zbyt wiele zapytań na godzinę. Proszę spróbuj ponownie później.')
		}
	}

	return (
		<>
			<button
				type="button"
				onClick={openModal}
				aria-label="Otwórz popup"
				className={cn(
					'fixed bottom-20 right-4 z-30 rounded-full bg-secondary p-3 text-white shadow-lg transition-all duration-300 hover:scale-110 hover:bg-primary hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2',
					!neverOpened ? 'scale-100 opacity-100' : 'pointer-events-none scale-0 opacity-0'
				)}
			>
				<HiInformationCircle size={24} />
			</button>

			<Transition appear show={isOpen} as={Fragment}>
				<Dialog as="div" className="relative z-50" onClose={closeModal}>
					<Transition.Child
						as={Fragment}
						enter="ease-out duration-300"
						enterFrom="opacity-0"
						enterTo="opacity-100"
						leave="ease-in duration-200"
						leaveFrom="opacity-100"
						leaveTo="opacity-0"
					>
						<div className="fixed inset-0 bg-black/60 backdrop-blur-sm" />
					</Transition.Child>

					<div className="fixed inset-0 overflow-y-auto">
						<div className="flex min-h-full items-center justify-center p-4 text-center">
							<Transition.Child
								as={Fragment}
								enter="ease-out duration-300"
								enterFrom="opacity-0 scale-95"
								enterTo="opacity-100 scale-100"
								leave="ease-in duration-200"
								leaveFrom="opacity-100 scale-100"
								leaveTo="opacity-0 scale-95"
							>
								<Dialog.Panel className="transform overflow-hidden rounded-2xl bg-primary bg-shapes bg-cover bg-bottom p-8 text-left align-middle shadow-xl transition-all">
									<button
										className="absolute right-8 top-8 rounded-full p-1 text-white/80 transition-colors hover:bg-white/20 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/50"
										onClick={closeModal}
										aria-label="Zamknij modal"
									>
										<HiXMark size={24} />
									</button>
									<Dialog.Title
										as="h3"
										className="mb-4 text-center font-rubik text-2xl font-bold text-white sm:text-4xl"
									>
										{data.title.split('\n').map((line, index) => (
											<Fragment key={index}>
												{line}
												{index < data.title.split('\n').length - 1 && <br />}
											</Fragment>
										))}
									</Dialog.Title>
									<div className="flex h-full w-full flex-col items-center gap-4 rounded-lg bg-white p-4 sm:p-8">
										<h3 className="text-md text-center font-bold text-secondary sm:text-xl">
											{data.subtitle.split('\\n').map((line, index) => (
												<Fragment key={index}>
													{line}
													{index < data.subtitle.split('\\n').length - 1 && <br />}
												</Fragment>
											))}
										</h3>
										<div className="flex w-full justify-around text-sm sm:text-base">
											{data.cities.map((city) => (
												<p className="text-center text-gray-600" key={city.name}>
													<span className="font-semibold text-gray-800">{city.name}: </span>
													{city.date && (
														<>
															{' '}
															+
															<br />
															{city.date}
														</>
													)}
													{city.address && (
														<>
															<br />
															{city.address}
														</>
													)}
												</p>
											))}
										</div>
										<h4 className="text-center text-xs font-semibold text-gray-600 sm:text-sm">
											{data.inputDescription}
										</h4>
										<form
											className="grid w-full place-items-center gap-6"
											onSubmit={(e) => handleForm(e)}
										>
											<div className="flex w-full gap-3 max-sm:flex-col">
												<div className="w-full">
													<select
														required
														value={city}
														onChange={(e) => setCity(e.target.value)}
														id="city"
														className="h-12 w-full rounded-lg border border-gray-300 bg-white px-4 text-sm text-gray-700 shadow-sm transition-all duration-200 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 hover:border-gray-400 sm:text-base"
													>
														{data.cities.map((city) => (
															<option key={city.name} value={city.name}>
																{city.name}
															</option>
														))}
													</select>
												</div>
												<div className="w-full">
													<input
														type="tel"
														id="tel"
														pattern="^([/+ -]*\d){9,}[/+ -]*$"
														className="h-12 w-full rounded-lg border border-gray-300 bg-white px-4 text-sm text-gray-700 shadow-sm transition-all duration-200 placeholder:text-gray-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 hover:border-gray-400 sm:text-base"
														placeholder="Numer telefonu"
														required
														value={phonenumber}
														onChange={(e) => setPhonenumber(e.target.value)}
													/>
												</div>
											</div>

											<button
												type="submit"
												className="btn btn-inverse disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:shadow-md"
												disabled={isLoading}
											>
												{isLoading ? 'Wysyłanie...' : data.buttonText}
												<HiPaperAirplane size={16} className="ml-2" />
											</button>

											<div className="flex items-start gap-3">
												<input
													className="mt-1 h-4 w-4 rounded border-gray-300 text-primary transition-colors focus:ring-2 focus:ring-primary/20 focus:ring-offset-0"
													type="checkbox"
													name="accept"
													id="accept"
													required
													checked={accepted}
													onChange={() => setAccepted((prev) => !prev)}
												/>
												<label htmlFor="accept" className="text-xs text-gray-600 leading-relaxed cursor-pointer">
													{data.acceptTerms}
												</label>
											</div>
										</form>

										{/* Success/Error Messages */}
										{success && (
											<div className="mt-6 rounded-lg bg-green-50 border border-green-200 p-4">
												<p className="text-center font-semibold text-green-800">
													{data.successMessage}
												</p>
											</div>
										)}

										{customMessage && (
											<div className="mt-6 rounded-lg bg-red-50 border border-red-200 p-4">
												<p className="text-center font-semibold text-red-800">
													{customMessage}
												</p>
											</div>
										)}

										<div className="mt-2 border-t border-gray-200 pt-6">
											<p className="text-center text-sm text-gray-600">
												{data.bottomInfo}
											</p>
										</div>
									</div>
								</Dialog.Panel>
							</Transition.Child>
						</div>
					</div>
				</Dialog>
			</Transition>
		</>
	)
}
