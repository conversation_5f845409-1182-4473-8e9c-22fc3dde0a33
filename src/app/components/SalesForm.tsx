'use client'
import { cn } from '@/utils/style'
import { useState } from 'react'
import { HiPaperAirplane } from 'react-icons/hi2'

export default function SalesForm() {
	const [city, setCity] = useState<string>('Warszawa')
	const [phonenumber, setPhonenumber] = useState<string>('')
	const [email, setEmail] = useState<string>('')
	const [fullName, setFullName] = useState<string>('')
	const [accepted, setAccepted] = useState<boolean>(false)
	const [isLoading, setIsLoading] = useState<boolean>(false)
	const [success, setSuccess] = useState<boolean>(false)
	const cities = [{ name: 'Warszawa' }, { name: 'Lublin' }]
	async function handleForm(event: React.FormEvent<HTMLFormElement>) {
		event.preventDefault()
		if (!accepted) return
		setIsLoading(true)
		const response = await fetch('/api/sales-popup', {
			method: 'POST',
			body: JSON.stringify({ city, phonenumber, fullName, email }),
			headers: {
				'Content-Type': 'application/json'
			}
		})
		setIsLoading(false)
		if (response.ok) {
			setSuccess(true)
			setTimeout(() => {
				setEmail('')
				setPhonenumber('')
				setFullName('')
				setSuccess(false)
			}, 2000)
		}
	}

	return (
		<section className="w-full bg-stepper bg-center px-6 py-24">
			<div className="mx-auto w-full max-w-2xl scroll-mt-24" id="sales-form">
				<div className="flex h-full w-full flex-col items-center gap-4 rounded-3xl bg-white p-6 shadow ring-1 ring-black/5 md:p-16">
					<div className="mb-4 space-y-2">
						<h3 className="text-center font-rubik text-xl font-bold sm:text-3xl">
							Uzupełnij formularz kontaktowy
						</h3>
						<p className="text-center text-gray-500">
							Prześlij formularz kontaktowy, a my zadzwonimy do Ciebie w ciągu 24 godzin
						</p>
					</div>
					<form className="grid w-full place-items-center gap-4" onSubmit={(e) => handleForm(e)}>
						<div className="grid w-full gap-2 sm:grid-cols-2">
							<input
								type="text"
								className="text-gray-60 h-10 w-full rounded-lg bg-gray-50 px-4 shadow-sm outline-none ring-1 ring-black/10 transition-all duration-75 focus:ring-primary sm:text-sm"
								placeholder="Imie i nazwisko"
								required
								value={fullName}
								onChange={(e) => setFullName(e.target.value)}
							/>
							<input
								type="tel"
								id="tel"
								pattern="^([/+ -]*\d){9,}[/+ -]*$"
								className="text-gray-60 h-10 w-full rounded-lg bg-gray-50 px-4 shadow-sm outline-none ring-1 ring-black/10 transition-all duration-75 focus:ring-primary sm:text-sm"
								placeholder="Numer telefonu"
								required
								value={phonenumber}
								onChange={(e) => setPhonenumber(e.target.value)}
							/>
							<input
								type="email"
								className="text-gray-60 h-10 w-full rounded-lg bg-gray-50 px-4 shadow-sm outline-none ring-1 ring-black/10 transition-all duration-75 focus:ring-primary sm:text-sm"
								placeholder="E-mail"
								required
								value={email}
								onChange={(e) => setEmail(e.target.value)}
							/>
							<select
								required
								value={city}
								onChange={(e) => setCity(e.target.value)}
								id="city"
								className="text-gray-60 h-10 w-full rounded-lg bg-gray-50 px-4 shadow-sm outline-none ring-1 ring-black/10 transition-all duration-75 focus:ring-primary sm:text-sm"
							>
								{cities.map((city) => (
									<option key={city.name} value={city.name}>
										{city.name}
									</option>
								))}
							</select>
						</div>
						<button className="btn btn-inverse disabled:opacity-50" disabled={isLoading}>
							Wyślij <HiPaperAirplane size={16} className="ml-2" />
						</button>
						<div className="flex items-center gap-4">
							<label className="flex items-center gap-2 text-xs text-gray-600">
								<input
									className="h-4 w-4 accent-primary"
									type="checkbox"
									name="accept"
									id="accept"
									required
									checked={accepted}
									onChange={() => setAccepted((prev) => !prev)}
								/>
								Wyrażam zgodę na przetwarzanie mobilnych danych osobowych podanych na powyższym
								formularzu
							</label>
						</div>
					</form>
					<h3 className={cn('font-bold text-green-600 last:text-lg', !success && 'hidden')}>
						Udało się! Dziękujemy za przesłanie formularza!
					</h3>
					{/* <h4 className="font-semibold text-gray-600">{data.bottomInfo}</h4> */}
				</div>
			</div>
		</section>
	)
}
