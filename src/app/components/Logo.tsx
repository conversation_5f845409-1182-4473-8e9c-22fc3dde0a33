import { cn } from '@/utils/style'
export default function Logo({
	alwaysWhite,
	isAtTheTop
}: {
	alwaysWhite?: boolean
	isAtTheTop?: boolean
}) {
	return (
		<svg
			data-name="ts_logo"
			xmlns="http://www.w3.org/2000/svg"
			viewBox="0 0 857.03 304.17"
			className={cn(
				'w-full',
				alwaysWhite ? 'fill-white' : 'fill-primary',
				isAtTheTop && 'fill-white'
			)}
		>
			<path d="M13.14 46v239h240V46Zm224 223h-49v-17h33v-16H78v16h94.14v17h-143v-73h17v56h16v-91h-16v19h-17V62h144v15h-96v16h128v96h16V77h-32V62h48Z" />
			<path d="M162 145v16h-18.14v59H78.14V110H128v16H94.14v78h33.72v-43H110v-16h52z" />
			<path d="M221 204v16h-63v-16h14.14v-78h-29.26v-16H188v12h.14v82H221zM46.14 77h16v68h-16zM373.64 69.34h-31.08v89h-24.62v-89H287v-20h86.63ZM453.66 158.33h-65.35v-109h62.85v20h-38.3v24.25h35.64v19.91h-35.64v24.93h40.8ZM549.26 154.46q-11.93 5.78-31.15 5.77-25.08 0-39.44-14.74T464.3 106.2q0-26.15 16.15-42.41t41.91-16.26q16 0 26.9 4v23.66a47.7 47.7 0 0 0-24.92-6.53q-15.36 0-24.78 9.65t-9.42 26.14q0 15.81 8.89 25.19T523 139a51 51 0 0 0 26.29-7ZM662.57 158.33H638V114h-45.19v44.38h-24.55v-109h24.55v43.44H638V49.36h24.62ZM785.53 158.33h-24.78l-44.91-68.47a99.83 99.83 0 0 1-5.47-9h-.3q.6 5.78.61 17.63v59.88H687.5v-109h26.44l43.24 66.34q3 4.48 5.47 8.89h.35a115.42 115.42 0 0 1-.61-15V49.36h23.18ZM835 158.33h-24.55v-109H835ZM292.63 277.93v-15a23.43 23.43 0 0 0 4.81 3.32 38 38 0 0 0 5.92 2.48 46.18 46.18 0 0 0 6.26 1.56 33.09 33.09 0 0 0 5.76.56q9.21 0 13.71-3.07a10.09 10.09 0 0 0 4.51-8.89 9.7 9.7 0 0 0-1.5-5.43 15.7 15.7 0 0 0-4.21-4.2 43.51 43.51 0 0 0-6.34-3.65c-2.44-1.17-5-2.39-7.82-3.65q-4.49-2.4-8.35-4.85a35.52 35.52 0 0 1-6.71-5.43 22.66 22.66 0 0 1-4.48-6.71 22 22 0 0 1-1.62-8.77 20.28 20.28 0 0 1 2.79-10.77 23.54 23.54 0 0 1 7.36-7.54 33.51 33.51 0 0 1 10.39-4.42A49.44 49.44 0 0 1 325 192q13.8 0 20.12 3.13v14.41q-7.49-5.4-19.26-5.4a34.42 34.42 0 0 0-6.47.62 19.3 19.3 0 0 0-5.77 2 12.64 12.64 0 0 0-4.14 3.62 8.89 8.89 0 0 0-1.59 5.34 10.07 10.07 0 0 0 1.23 5.09 13 13 0 0 0 3.58 3.92 37.06 37.06 0 0 0 5.77 3.47q3.4 1.68 7.88 3.65 4.61 2.38 8.68 5a41.44 41.44 0 0 1 7.18 5.82 25.58 25.58 0 0 1 4.9 7.09 20.75 20.75 0 0 1 1.81 8.86 20.81 20.81 0 0 1-10 18.77 32.54 32.54 0 0 1-10.52 4.17 59.28 59.28 0 0 1-12.58 1.29 52.3 52.3 0 0 1-5.43-.34q-3.21-.33-6.59-1a62 62 0 0 1-6.38-1.56 20.22 20.22 0 0 1-4.79-2.02ZM426.4 277.75q-9.94 5.14-24.72 5.15-19.14 0-30.67-12.08t-11.53-31.71q0-21.11 13-34.11t32.79-13q12.75 0 21.16 3.62v14.47a37.82 37.82 0 0 0-19.63-5.27q-14.29 0-23.15 9.14t-8.87 24.41q0 14.53 8.28 23.15t21.78 8.62q12.45 0 21.59-5.89ZM513.74 281.43h-14.66v-38.15h-41.4v38.15h-14.6v-88h14.6v37h41.4v-37h14.66ZM571.7 282.9q-19 0-30.45-12.33t-11.44-32.08q0-21.23 11.65-33.85T573.11 192q18.52 0 29.84 12.27t11.32 32.08q0 21.53-11.59 34T571.7 282.9Zm.68-78.08a24.44 24.44 0 0 0-19.66 9q-7.58 9-7.58 23.73t7.36 23.62a23.74 23.74 0 0 0 19.26 8.95q12.65 0 19.94-8.52t7.3-23.86q0-15.76-7.09-24.35t-19.53-8.57ZM666.65 282.9q-19 0-30.45-12.33t-11.44-32.08q0-21.23 11.65-33.85T668.06 192q18.53 0 29.84 12.27t11.31 32.08q0 21.53-11.59 34t-30.97 12.55Zm.67-78.08a24.46 24.46 0 0 0-19.66 9q-7.57 9-7.57 23.73t7.36 23.62a23.74 23.74 0 0 0 19.26 8.95q12.63 0 19.93-8.52t7.3-23.86q0-15.76-7.08-24.35t-19.54-8.57ZM774.05 281.43h-48.7v-88h14.59v75.67h34.11ZM782.69 277.93v-15a23.51 23.51 0 0 0 4.82 3.32 37.63 37.63 0 0 0 5.92 2.48 46 46 0 0 0 6.25 1.56 33.29 33.29 0 0 0 5.77.56q9.19 0 13.71-3.07a10.11 10.11 0 0 0 4.51-8.89 9.71 9.71 0 0 0-1.51-5.43 15.51 15.51 0 0 0-4.2-4.2 43.13 43.13 0 0 0-6.35-3.65q-3.65-1.76-7.82-3.65-4.47-2.4-8.34-4.85a35.52 35.52 0 0 1-6.71-5.43 22.47 22.47 0 0 1-4.48-6.71 21.79 21.79 0 0 1-1.63-8.77 20.28 20.28 0 0 1 2.79-10.77 23.65 23.65 0 0 1 7.36-7.54 33.67 33.67 0 0 1 10.4-4.42 49.38 49.38 0 0 1 11.9-1.44q13.8 0 20.12 3.13v14.41q-7.49-5.4-19.26-5.4a34.34 34.34 0 0 0-6.47.62 19.3 19.3 0 0 0-5.77 2 12.44 12.44 0 0 0-4.14 3.62 8.89 8.89 0 0 0-1.59 5.34 10.17 10.17 0 0 0 1.22 5.09 13.15 13.15 0 0 0 3.59 3.92 36.59 36.59 0 0 0 5.77 3.47c2.27 1.12 4.89 2.34 7.88 3.65q4.61 2.38 8.68 5a41 41 0 0 1 7.17 5.82 25.62 25.62 0 0 1 4.91 7.09 20.75 20.75 0 0 1 1.81 8.86 20.79 20.79 0 0 1-10 18.77 32.36 32.36 0 0 1-10.52 4.17 59.12 59.12 0 0 1-12.57 1.29 52.3 52.3 0 0 1-5.43-.34q-3.21-.33-6.59-1a61.4 61.4 0 0 1-6.38-1.56 20.5 20.5 0 0 1-4.82-2.05Z" />
		</svg>
	)
}
