import { Post as PostType } from '@/types'
import { Post } from '../blog/Post'

export default function Masonry({ array }: { array: PostType[] }) {
	function reorder<T>(arr: T[], numCols: number): T[][] {
		const columns = new Array(numCols).fill([])
		let column = 0
		for (let i = 0; i < arr.length; i++) {
			columns[column] = [...columns[column], arr[i]]
			column++
			if (column === numCols) column = 0
		}
		return columns
	}

	return (
		<>
			<div className="hidden w-full gap-6 xl:flex">
				{reorder(array, 4).map((row, i) => (
					<div key={i} className="w-full">
						{row.map((item) => item && <Post key={item.slug} post={item} />)}
					</div>
				))}
			</div>
			<div className="hidden gap-6 md:flex xl:hidden">
				{reorder(array, 3).map((col, i) => (
					<div key={i} className="w-full">
						{col.map((item) => item && <Post key={item.slug} post={item} />)}
					</div>
				))}
			</div>
			<div className="hidden gap-6 sm:flex md:hidden">
				{reorder(array, 2).map((col, i) => (
					<div key={i} className="w-full">
						{col.map((item) => item && <Post key={item.slug} post={item} />)}
					</div>
				))}
			</div>
			<div className="flex gap-6 sm:hidden">
				{reorder(array, 1).map((col, i) => (
					<div key={i} className="w-full">
						{col.map((item) => item && <Post key={item.slug} post={item} />)}
					</div>
				))}
			</div>
		</>
	)
}
