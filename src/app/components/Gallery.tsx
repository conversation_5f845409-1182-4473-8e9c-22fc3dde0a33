import { getGallerySection } from '@/utils/api'
import { getAssetPath } from '@/utils/assets'
import Image from 'next/image'
import Link from 'next/link'
import Carousel from './Carousel'

export default async function Gallery() {
	const { sectionData, images } = await getGallerySection()
	return (
		<section>
			<div className="container mx-auto px-4 py-12 lg:px-12" aria-labelledby="galleryTitle">
				<h2 id="galleryTitle" className="title text-primary">
					{sectionData.title}
				</h2>
			</div>
			<div className="relative flex flex-col items-center justify-center gap-12 overflow-hidden bg-primary bg-[url('https://dev.technischools.com/assets/link/6426d06be3eb8f5fcd07e8e0')] bg-cover bg-center pb-16 pt-16">
				<div className="absolute inset-0 bg-gradient-to-b from-primary/80 via-primary/60 to-primary/80"></div>
				<div className="container relative mx-auto grid gap-12 px-4 lg:px-12">
					<Carousel array={images} slideWidths={[{ breakpoint: 0, width: 20 * 16 }]}>
						{images.map(({ img }, index) => (
							<div key={index} className="group">
								<div className="relative flex aspect-[4/3] w-80 snap-start snap-always overflow-hidden rounded-2xl shadow-xl transition-all duration-300 group-hover:shadow-2xl group-hover:scale-105">
									<div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
									<Image
										src={getAssetPath(img.path)}
										alt={img.description}
										width={320}
										height={320}
										className="h-full w-full object-cover object-right-top transition-transform duration-500 group-hover:scale-110"
									/>
								</div>
							</div>
						))}
					</Carousel>
				</div>
				<Link
					href="/gallery"
					className="group relative z-10 rounded-xl bg-white/10 px-8 py-4 font-medium text-white backdrop-blur-sm transition-all duration-300 hover:bg-white/20 hover:scale-105 hover:shadow-lg"
				>
					<span className="relative z-10">{sectionData.anchorText}</span>
					<div className="absolute inset-0 rounded-xl bg-gradient-to-r from-white/5 to-white/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
				</Link>
			</div>
		</section>
	)
}
