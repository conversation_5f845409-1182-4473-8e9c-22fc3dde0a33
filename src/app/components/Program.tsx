import Image from 'next/image'
import { cn } from '@/utils/style'
import { getTechniDifferences } from '@/utils/api'
import { getAssetPath } from '@/utils/assets'

export default async function Program() {
	const { title, differences: data } = await getTechniDifferences()
	let count = 1
	const differences = data.map((section) => {
		return {
			...section,
			differences: section.differences.map((diff) => {
				return { ...diff, count: count++ }
			})
		}
	})

	return (
		<section
			className="relative w-full scroll-smooth bg-primary bg-shapes bg-contain py-16"
			aria-labelledby="techniDifferencesTitle"
		>
			<div className="absolute inset-0 bg-gradient-to-b from-primary/90 via-primary/80 to-primary/90"></div>
			<div className="container relative mx-auto mb-8 px-4 lg:px-12">
				<h2
					id="techniDifferencesTitle"
					className="w-full pb-16 font-rubik text-xl font-bold tracking-tight text-white sm:text-3xl lg:text-5xl"
				>
					{title}
				</h2>
			</div>
			{differences.map((section, index) => (
				<div key={index} className="container relative mx-auto flex px-4 lg:px-12">
					<div className={cn('w-40 border-white/20 sm:w-60 sm:border-r-2', index === 0 && 'mt-8')}>
						<div className="sticky top-80 grid w-12 text-white" key={index}>
							<span className="text-sm font-light tracking-wider text-white/70 sm:text-xl">0{index + 1}.</span>
							<h2 className="text-lg font-semibold leading-tight tracking-tight sm:text-2xl md:text-4xl">
								{section.title}
							</h2>
							<span className="mt-4 h-1.5 max-w-[3rem] rounded-full bg-gradient-to-r from-secondary to-secondary/80 shadow-lg sm:max-w-[6rem]" />
						</div>
					</div>
					<div className="grid w-full">
						{section.differences.map((element, differenceIndex) => (
							<div
								key={differenceIndex}
								className={cn(
									'group relative flex h-48 w-full transition-all duration-300 hover:scale-[1.02] sm:pl-6',
									section.differences.length - 1 === differenceIndex &&
										data.length - 1 === index &&
										'overflow-hidden'
								)}
							>
								<div className="mr-3 hidden w-3/5 pt-4 text-right text-white sm:block">
									{element.count % 2 === 0 ? (
										<div className="float-right mr-6 rounded-xl bg-white/10 p-3 backdrop-blur-sm transition-all duration-300 group-hover:bg-white/20 group-hover:scale-110">
											<Image
												src={getAssetPath(element.icon.path)}
												alt={'icon ' + element.icon.description}
												height={48}
												width={48}
												className="aspect-square h-12 object-contain transition-transform duration-300 group-hover:scale-110"
											/>
										</div>
									) : (
										<p className="pt-4 leading-relaxed text-white/90 transition-colors duration-300 group-hover:text-white">{element.description}</p>
									)}
								</div>
								<div className="pt-4">
									<div className="relative z-10 flex h-10 w-10 items-center justify-center">
										<div className="h-4 w-4 rounded-full bg-secondary shadow-lg transition-all duration-300 group-hover:scale-125 group-hover:shadow-xl"></div>
										<div className="absolute h-6 w-6 rounded-full bg-white/20 transition-all duration-300 group-hover:scale-150 group-hover:bg-white/30"></div>
									</div>
									<span className="absolute -mt-6 ml-[1.19rem] h-full w-0.5 bg-gradient-to-b from-white/30 to-white/10 transition-all duration-300 group-hover:from-white/50 group-hover:to-white/20" />
								</div>
								<div className="ml-6 mt-4 w-40 text-white sm:w-3/5">
									{element.count % 2 === 0 ? (
										<p className="leading-relaxed text-white/90 transition-colors duration-300 group-hover:text-white">{element.description}</p>
									) : (
										<>
											<div className="mr-3 hidden w-fit rounded-xl bg-white/10 p-3 backdrop-blur-sm transition-all duration-300 group-hover:bg-white/20 group-hover:scale-110 sm:block">
												<Image
													src={getAssetPath(element.icon.path)}
													alt={'icon ' + element.icon.description}
													height={48}
													width={48}
													className="aspect-square object-contain transition-transform duration-300 group-hover:scale-110"
												/>
											</div>
											<p className="leading-relaxed text-white/90 transition-colors duration-300 group-hover:text-white block sm:hidden">{element.description}</p>
										</>
									)}
								</div>
							</div>
						))}
					</div>
				</div>
			))}
		</section>
	)
}
