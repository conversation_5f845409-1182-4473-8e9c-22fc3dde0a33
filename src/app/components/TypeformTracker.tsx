'use client'
import { useTypeformTracker } from '@/utils/typeform-tracker'
import Script from 'next/script'

export default function TypeformTracker({
	formName,
	includeScript = true
}: {
	formName: string
	includeScript?: boolean
}) {
	useTypeformTracker({
		onStart: () => {
			window.dataLayer.push({ event: `${formName}_start` })
		},
		onSubmit: () => {
			window.dataLayer.push({ event: `${formName}_submit` })
		}
	})

	return includeScript ? <Script src="//embed.typeform.com/next/embed.js"></Script> : null
}
