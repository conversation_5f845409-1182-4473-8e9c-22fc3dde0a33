import { FC } from 'react'
import { motion, Variants, HTMLMotionProps } from 'framer-motion'

interface Props extends HTMLMotionProps<'div'> {
	text: string
	delay?: number
	replay: boolean
	duration?: number
}

const JobTitle: FC<Props> = ({ text, delay = 0, duration = 0.6, replay, ...props }: Props) => {
	const container: Variants = {
		hidden: {
			opacity: 0
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const child: Variants = {
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				type: 'linear',
				duration: 0.6
			}
		},
		hidden: {
			opacity: 0,
			y: 150,
			transition: {
				type: 'linear',
				duration: 0.6
			}
		}
	}

	return (
		<motion.div
			variants={container}
			initial="hidden"
			animate={replay ? 'visible' : 'hidden'}
			{...props}
			className="flex h-12 overflow-hidden lg:h-auto"
		>
			<motion.h3
				variants={child}
				className="z-10 inline-block text-base font-medium leading-relaxed text-gray-600 lg:w-2/3 lg:text-lg xl:pb-3"
			>
				{text}
			</motion.h3>
		</motion.div>
	)
}

export default JobTitle
