import { FC } from 'react'
import { motion, Variants, HTMLMotionProps } from 'framer-motion'

interface Pro<PERSON> extends HTMLMotionProps<'div'> {
	delay?: number
	replay: boolean
	duration?: number
	src: string
}

const AvatarBackground: FC<Props> = ({
	src,
	delay = 0.1,
	duration = 0.2,
	replay,
	...props
}: Props) => {
	const container: Variants = {
		hidden: {
			opacity: 0
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const container2: Variants = {
		hidden: {
			opacity: 1
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const child: Variants = {
		visible: {
			opacity: 1,
			x: 0,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			opacity: 0,
			x: -400,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}

	const child2: Variants = {
		visible: {
			opacity: 1,
			x: -400,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			opacity: 1,
			x: 50,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}

	const childTablet: Variants = {
		visible: {
			opacity: 1,
			x: 0,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			opacity: 0,
			x: -750,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}

	const childTablet2: Variants = {
		visible: {
			opacity: 1,
			x: -750,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			opacity: 1,
			x: 100,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}

	const childMobile: Variants = {
		visible: {
			opacity: 1,
			x: 0,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			opacity: 0,
			x: -750,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}

	const childMobile2: Variants = {
		visible: {
			opacity: 1,
			x: -350,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			opacity: 1,
			x: 150,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}

	return (
		<>
			<motion.div
				variants={container}
				initial="hidden"
				animate={replay ? 'visible' : 'hidden'}
				{...props}
				className="-z-10 hidden h-full w-full overflow-hidden lg:flex"
			>
				<motion.img
					src={src}
					alt="Staff background"
					variants={child}
					className="h-1/2 w-full object-cover lg:h-full"
				/>
			</motion.div>
			<motion.div
				variants={container2}
				initial="hidden"
				animate={replay ? 'visible' : 'hidden'}
				{...props}
				className="relative -top-full flex w-full overflow-hidden lg:h-full"
			>
				<motion.div variants={child2} className="h-full w-full bg-white" />
			</motion.div>
			{/* for mobile screen */}
			<motion.div
				variants={container}
				initial="hidden"
				animate={replay ? 'visible' : 'hidden'}
				{...props}
				className="-z-10 flex h-full w-full overflow-hidden sm:hidden"
			>
				<motion.img
					src={src}
					alt="Staff background"
					variants={childMobile}
					className="h-full w-full object-cover"
				/>
			</motion.div>
			<motion.div
				variants={container2}
				initial="hidden"
				animate={replay ? 'visible' : 'hidden'}
				{...props}
				className="relative -top-full flex h-full w-full overflow-hidden sm:hidden"
			>
				<motion.div variants={childMobile2} className="h-full w-full bg-white" />
			</motion.div>
			{/* for tablet screen */}
			<motion.div
				variants={container}
				initial="hidden"
				animate={replay ? 'visible' : 'hidden'}
				{...props}
				className="-z-10 h-full w-full overflow-hidden sm:flex lg:hidden"
			>
				<motion.img
					src={src}
					alt="Staff background"
					variants={childTablet}
					className="h-full w-full object-cover"
				/>
			</motion.div>
			<motion.div
				variants={container2}
				initial="hidden"
				animate={replay ? 'visible' : 'hidden'}
				{...props}
				className="relative -top-full flex h-full w-full overflow-hidden"
			>
				<motion.div variants={childTablet2} className="h-full w-full bg-white" />
			</motion.div>
		</>
	)
}

export default AvatarBackground
