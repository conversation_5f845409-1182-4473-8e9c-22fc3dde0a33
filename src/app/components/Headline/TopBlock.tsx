import { FC } from 'react'
import { motion, Variants, HTMLMotionProps } from 'framer-motion'

interface Pro<PERSON> extends HTMLMotionProps<'div'> {
	text: string
	delay?: number
	replay: boolean
	duration?: number
}

const TopBlock: FC<Props> = ({ text, delay = 0.2, duration = 0.1, replay, ...props }: Props) => {
	const container: Variants = {
		hidden: {
			opacity: 0
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const child: Variants = {
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			opacity: 0,
			y: -100,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}

	return (
		<motion.h2
			variants={container}
			initial="hidden"
			animate={replay ? 'visible' : 'hidden'}
			{...props}
			className="flex items-center justify-center overflow-hidden"
		>
			<motion.span
				variants={child}
				className="relative z-10 inline-block rounded-2xl bg-white/95 px-6 py-3 text-center text-lg font-bold uppercase tracking-wider text-indigo-900 shadow-xl backdrop-blur-sm sm:px-10 sm:py-4 sm:text-2xl sm:tracking-widest md:text-3xl lg:px-16"
			>
				<div className="absolute -inset-1 rounded-2xl bg-gradient-to-r from-primary/20 to-secondary/20 opacity-50 blur"></div>
				<span className="relative z-10">{text}</span>
			</motion.span>
		</motion.h2>
	)
}

export default TopBlock
