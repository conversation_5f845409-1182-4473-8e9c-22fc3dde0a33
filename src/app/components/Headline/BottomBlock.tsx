import { FC } from 'react'
import { motion, Variants, HTMLMotionProps } from 'framer-motion'
import Link from 'next/link'

interface Pro<PERSON> extends HTMLMotionProps<'div'> {
	text?: string
	delay?: number
	replay: boolean
	duration?: number
	anchorText?: string
	anchorLink?: string
}

const BottomBlock: FC<Props> = ({
	text,
	anchorText,
	anchorLink,
	delay = 0.2,
	duration = 0.1,
	replay,
	...props
}: Props) => {
	const container: Variants = {
		hidden: {
			opacity: 0
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const child: Variants = {
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			opacity: 0,
			y: 100,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}

	return (
		<motion.h2
			variants={container}
			initial="hidden"
			animate={replay ? 'visible' : 'hidden'}
			{...props}
			className={
				'flex justify-center overflow-hidden ' +
				(anchorText ? 'relative mt-8 h-fit' : 'items-center')
			}
		>
			{anchorText && anchorLink ? (
				<motion.a
					href={anchorLink}
					variants={child}
					className="group relative z-10 inline-block overflow-hidden rounded-2xl bg-gradient-to-r from-secondary via-secondary to-secondary/90 px-8 py-4 text-center text-lg font-bold uppercase tracking-wider text-white shadow-2xl transition-all duration-300 hover:scale-110 hover:shadow-3xl sm:px-12 sm:py-5 sm:text-xl md:text-2xl lg:px-16"
				>
					<div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
					<span className="relative z-10 flex items-center justify-center gap-2">
						{anchorText}
						<svg className="h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
						</svg>
					</span>
					<div className="absolute -inset-1 rounded-2xl bg-gradient-to-r from-secondary to-primary opacity-0 blur transition-opacity duration-300 group-hover:opacity-30"></div>
				</motion.a>
			) : (
				<motion.span
					variants={child}
					className="z-10 inline-block rounded-2xl bg-white/95 px-6 py-3 text-center text-lg font-bold uppercase tracking-wider text-indigo-900 shadow-xl backdrop-blur-sm sm:px-10 sm:py-4 sm:text-2xl sm:tracking-widest md:text-3xl lg:px-16"
				>
					{text}
				</motion.span>
			)}
		</motion.h2>
	)
}

export default BottomBlock
