import { getHeroSection } from '@/utils/api'
import { cn } from '@/utils/style'
import { Montserrat } from 'next/font/google'
import { ComposedBlocks } from './ComposedBlocks'

const montserrat = Montserrat({
	subsets: ['latin-ext'],
	weight: ['300', '400', '500', '700']
})

export default async function Headline() {
	const data = await getHeroSection()
	let anchorText = 'Zapisz się teraz'
	let anchorLink = '#sales-form'
	data.content.forEach(item => {
		if (item.anchorText && item.anchorLink) {
			anchorText = item.anchorText
			anchorLink = item.anchorLink
		}
	})
	return (
		<section
			className={cn(
				'relative grid max-h-screen place-items-center overflow-hidden bg-headline py-32 pb-64 text-center transition-all md:h-screen 2xl:h-fit 2xl:pt-48 2xl:pb-64',
				montserrat.className
			)}
			aria-label="Techni Schools Hero Section"
		>
			
			{/* Enhanced gradient overlays for depth */}
			<div className="absolute inset-0 bg-gradient-to-br from-black/40 via-black/20 to-black/40"></div>
			<div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/30"></div>

			{/* Animated background elements */}
			<div className="absolute inset-0 opacity-30">
				<div className="absolute top-1/4 left-1/4 h-64 w-64 rounded-full bg-gradient-to-r from-primary/20 to-secondary/20 blur-3xl animate-pulse"></div>
				<div className="absolute bottom-1/4 right-1/4 h-96 w-96 rounded-full bg-gradient-to-r from-secondary/20 to-primary/20 blur-3xl animate-pulse delay-1000"></div>
			</div>

			<ComposedBlocks data={data} />

			{/* Additional conversion elements - positioned to not affect slide transitions */}
			<div className="absolute bottom-8 left-1/2 w-full max-w-sm -translate-x-1/2 px-4 sm:bottom-16 sm:max-w-none sm:px-0">
				<div className="flex flex-col items-center gap-3 sm:gap-4">
					{/* Premium CTA Button */}
					<a
						href={anchorLink}
						className="group relative w-full overflow-hidden rounded-2xl bg-gradient-to-r from-secondary to-secondary/80 px-6 py-3 text-center text-base font-bold text-white shadow-2xl transition-all duration-300 hover:scale-105 hover:shadow-3xl sm:w-auto sm:px-8 sm:py-4 sm:text-lg sm:hover:scale-110"
					>
						<div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
						<span className="relative z-10 flex items-center justify-center gap-2">
							{anchorText}
							<svg className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1 sm:h-5 sm:w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
							</svg>
						</span>
					</a>
{/* TAILWIND BREAKPOINT INDICATOR */}
			{/* <div className="absolute top-0 left-0 w-4 h-4 bg-red-500 text-sm">
				<span className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 hidden 2xl:block text-white">2xl</span>
				<span className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 hidden xl:block 2xl:hidden text-white">xl</span>
				<span className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 hidden lg:block xl:hidden 2xl:hidden text-white">lg</span>
				<span className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 hidden md:block lg:hidden xl:hidden 2xl:hidden text-white">md</span>
				<span className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 hidden sm:block md:hidden lg:hidden xl:hidden 2xl:hidden text-white">sm</span>
				<span className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 hidden xs:block sm:hidden md:hidden lg:hidden xl:hidden 2xl:hidden text-white">xs</span>
			</div> */}
					{/* Social proof */}
					{/* <div className="flex items-center gap-2 rounded-xl bg-white/10 px-4 py-2 backdrop-blur-sm sm:gap-3 sm:px-6 sm:py-3">
						<div className="flex -space-x-1 sm:-space-x-2">
							<div className="h-6 w-6 rounded-full bg-gradient-to-r from-primary to-secondary border-2 border-white sm:h-8 sm:w-8"></div>
							<div className="h-6 w-6 rounded-full bg-gradient-to-r from-secondary to-primary border-2 border-white sm:h-8 sm:w-8"></div>
							<div className="h-6 w-6 rounded-full bg-gradient-to-r from-primary to-secondary border-2 border-white sm:h-8 sm:w-8"></div>
						</div>
						<span className="text-xs font-semibold text-white sm:text-sm">
							Dołącz do 500+ absolwentów
						</span>
					</div> */}

					{/* Urgency indicator */}
					<div className="flex items-center justify-center gap-2 text-white/80">
						<div className="h-1.5 w-1.5 rounded-full bg-red-500 animate-pulse sm:h-2 sm:w-2"></div>
						<span className="text-xs font-semibold sm:text-sm">Tylko 32 miejsca dostępne!</span>
					</div>
				</div>
			</div>
		</section>
	)
}
