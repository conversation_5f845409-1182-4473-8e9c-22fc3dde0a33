import { getApplyToday } from '@/utils/api'

export default async function ApplyToday() {
	const data = await getApplyToday()
	return (
		<section
			className="relative h-[28rem] w-full bg-headline bg-cover bg-center"
			aria-labelledby="applyTodayTitle"
		>
			<div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/60"></div>
			<div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
			<div className="container relative mx-auto grid h-full place-items-center gap-8 md:gap-20 px-4 py-16 md:grid-cols-2 lg:px-12">
				<div className="grid gap-10">
					<h3
						id="applyTodayTitle"
						className="max-w-xs text-center text-2xl font-bold leading-tight tracking-tight text-white drop-shadow-2xl md:max-w-md md:text-left md:text-4xl md:leading-tight"
					>
						{data.title}
					</h3>
					<p className="text-lg max-w-xs text-center font-medium leading-relaxed text-white/95 drop-shadow-lg md:max-w-md md:text-left md:text-xl md:leading-relaxed">
						{data.vacanciesInfo.split('{vacancies}')[0]}
						<span className="rounded-lg bg-gradient-to-r from-secondary to-secondary/80 px-3 py-1 text-xl font-bold text-white shadow-lg md:text-2xl">{data.vacancies}</span>
						{data.vacanciesInfo.split('{vacancies}')[1]}
					</p>
				</div>
				<div className="flex flex-col items-center gap-6">
					<a
						href="/apply"
						aria-label="Apply Now"
						className="group relative overflow-hidden rounded-2xl bg-gradient-to-r from-secondary to-secondary/80 px-12 py-6 text-xl font-bold uppercase text-white shadow-2xl transition-all duration-300 hover:scale-110 hover:shadow-3xl"
					>
						<div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
						<span className="relative z-10">{data.cta}</span>
					</a>
					<div className="flex items-center gap-2 text-white/80">
						<div className="h-2 w-2 animate-pulse rounded-full bg-green-400"></div>
						<span className="text-sm font-medium">
							Ograniczona liczba miejsc
						</span>
					</div>
				</div>
			</div>
		</section>
	)
}
