import { FC } from 'react'
import { motion, Variants, HTMLMotionProps } from 'framer-motion'

interface Props extends HTMLMotionProps<'div'> {
	delay?: number
	replay: boolean
	duration?: number
	src: string
}

const StepPhoto: FC<Props> = ({ src, delay = 0.2, duration = 0.3, replay, ...props }: Props) => {
	const container: Variants = {
		hidden: {
			opacity: 0
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const container2: Variants = {
		hidden: {
			opacity: 1
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const child: Variants = {
		visible: {
			opacity: 1,
			x: 0,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			opacity: 0,
			x: -400,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}

	const child2: Variants = {
		visible: {
			opacity: 1,
			x: -600,
			transition: {
				type: 'linear',
				duration: 1
			}
		},
		hidden: {
			opacity: 1,
			x: 150,
			transition: {
				type: 'linear',
				duration: 1
			}
		}
	}
	return (
		<>
			<motion.div
				variants={container}
				initial="hidden"
				animate={replay ? 'visible' : 'hidden'}
				{...props}
				className="flex h-full w-full overflow-hidden object-cover"
			>
				<motion.img
					src={src}
					alt="Why Choose Techni Schools Thumbnail"
					variants={child}
					className="z-10 h-full w-full object-cover"
					loading="lazy"
				/>
			</motion.div>
			<motion.div
				variants={container2}
				initial="hidden"
				animate={replay ? 'visible' : 'hidden'}
				{...props}
				className="relative -top-full flex h-full w-full overflow-hidden"
			>
				<motion.div variants={child2} className="z-10 h-full w-full bg-white" />
			</motion.div>
		</>
	)
}

export default StepPhoto
