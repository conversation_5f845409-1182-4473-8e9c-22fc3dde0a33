import { FC } from 'react'
import { motion, Variants, HTMLMotionProps } from 'framer-motion'
import htmr from 'htmr'

interface Props extends HTMLMotionProps<'div'> {
	text: string
	delay?: number
	replay: boolean
	duration?: number
}

const StepContent: FC<Props> = ({ text, delay = 0, duration = 0.3, replay, ...props }: Props) => {
	const container: Variants = {
		hidden: {
			opacity: 0
		},
		visible: (i: number = 1) => ({
			opacity: 1,
			transition: { staggerChildren: duration, delayChildren: i * delay }
		})
	}

	const child: Variants = {
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				type: 'linear',
				duration: 0.3
			}
		},
		hidden: {
			opacity: 0,
			y: 0,
			transition: {
				type: 'linear',
				duration: 0.3
			}
		}
	}

	return (
		<motion.div
			variants={container}
			initial="hidden"
			animate={replay ? 'visible' : 'hidden'}
			{...props}
			className="flex overflow-hidden"
		>
			<motion.div variants={child} className="content z-10 text-gray-500">
				{htmr(text)}
			</motion.div>
		</motion.div>
	)
}

export default StepContent
