import ComposedBlocks from './ComposedBlocks'
import { getWhyTechni } from '@/utils/api'

export default async function Stepper() {
	const { title, steps } = await getWhyTechni()
	return (
		<section
			className="bg-stepper bg-cover bg-center bg-no-repeat"
			id="why-techni"
			aria-labelledby="whyTechniTitle"
		>
			<div className="container mx-auto h-full gap-8 px-4 py-12 lg:place-content-center lg:px-12 2xl:min-h-fit 2xl:py-32">
				<h2 className="title scroll-mt-28 text-primary" id="whyTechniTitle">
					{title}
				</h2>
				<div className="mt-10 flex h-full flex-col justify-between overflow-hidden rounded-2xl border border-gray-200 bg-white shadow-xl sm:h-[36rem] lg:flex-row">
					<ComposedBlocks steps={steps} />
				</div>
			</div>
		</section>
	)
}
