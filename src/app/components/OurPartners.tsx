import { getPartners } from '@/utils/api'
import { getAssetPath } from '@/utils/assets'
import Image from 'next/image'

export default async function OurPartners() {
	const { partners, title } = await getPartners()
	return (
		<section
			className="container mx-auto px-4 py-12 lg:px-12 2xl:py-32"
			aria-labelledby="ourPartnersTitle"
		>
			<h2 id="ourPartnersTitle" className="title mb-12 text-primary">
				{title}
			</h2>
			<div className="grid grid-cols-2 gap-10 md:grid-cols-3 md:gap-x-28 md:gap-y-10 lg:gap-x-24 xl:gap-x-56 2xl:gap-x-96">
				{partners.map((partner, index) => (
					<a
						key={index}
						href={partner.url}
						target="_blank"
						rel="noreferrer"
						className="flex items-center justify-center"
					>
						<Image
							src={getAssetPath(partner.logo.path)}
							alt={partner.name}
							width={180}
							height={180}
						></Image>
					</a>
				))}
			</div>
		</section>
	)
}
