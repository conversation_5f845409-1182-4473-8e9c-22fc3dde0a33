'use client'
import { <PERSON><PERSON>Popup } from '@/types'
import { cn } from '@/utils/style'
import { useEffect, useState } from 'react'
import { HiXMark } from 'react-icons/hi2'

interface ConsentSettings {
	necessary: boolean
	analytics_storage: boolean
	ad_storage: boolean
	ad_user_data: boolean
	ad_personalization: boolean
}

export default function AcceptCookies({ data }: { data: CookiePopup }) {
	const [show, setShow] = useState(false)
	const [showDetails, setShowDetails] = useState(false)
	const [cookieAccepted, setCookieAccepted] = useState(false)
	const [settings, setSettings] = useState<ConsentSettings>({
		necessary: true,
		analytics_storage: true,
		ad_storage: true,
		ad_user_data: true,
		ad_personalization: true
	})

	useEffect(() => {
		// Set default consent state to 'denied' on initial load
		window.gtag('consent', 'default', {
			ad_storage: 'denied',
			analytics_storage: 'denied',
			ad_user_data: 'denied',
			ad_personalization: 'denied',
			wait_for_update: 500,
			security_storage: 'granted' // Allow necessary cookies
		})

		// Check if user has previously made a choice
		const checkCookieConsent = async () => {
			const response = await fetch('/api/cookie_notice_accepted')
			const data = await response.json()
			if (!data.cookieNoticeAccepted) {
				setShow(true)
			} else if (data.cookieSettings) {
				// If user has previously made a choice, restore their settings
				setSettings(data.cookieSettings)
				updateConsent(data.cookieSettings)
			}
		}
		checkCookieConsent()
	}, [])

	function updateConsent(settings: ConsentSettings) {
		window.gtag('consent', 'update', {
			analytics_storage: settings.analytics_storage ? 'granted' : 'denied',
			ad_storage: settings.ad_storage ? 'granted' : 'denied',
			ad_user_data: settings.ad_user_data ? 'granted' : 'denied',
			ad_personalization: settings.ad_personalization ? 'granted' : 'denied'
		})
	}

	const setCookie = async (settings: ConsentSettings) => {
		const res = await fetch('/api/cookie_notice_accepted', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ settings })
		})
		if (res.status === 200) {
			setCookieAccepted(true)
			setTimeout(() => setShow(false), 150)
		}
	}

	const acceptAll = async () => {
		const allEnabled = {
			...settings,
			analytics_storage: true,
			ad_storage: true,
			ad_user_data: true,
			ad_personalization: true
		}
		setSettings(allEnabled)
		updateConsent(allEnabled)
		await setCookie(allEnabled)
	}

	const rejectAll = async () => {
		const allDisabled = {
			...settings,
			analytics_storage: false,
			ad_storage: false,
			ad_user_data: false,
			ad_personalization: false
		}
		setSettings(allDisabled)
		updateConsent(allDisabled)
		await setCookie(allDisabled)
	}

	const savePreferences = async () => {
		updateConsent(settings)
		await setCookie(settings)
	}

	return (
		<div
			className={cn(
				'fixed bottom-0 z-40 w-full bg-zinc-800 px-4 py-6 text-white transition-opacity',
				!cookieAccepted ? 'opacity-100' : 'pointer-events-none opacity-0',
				show ? 'block' : 'hidden'
			)}
		>
			<div className="mx-auto max-w-4xl">
				<div className="mb-4 flex items-start justify-between">
					<div className="mr-4">
						<p className="mb-2 text-sm">
							Ta strona używa plików cookie i przetwarza dane osobowe w celu personalizacji treści i
							reklam oraz analizy ruchu na stronie. Korzystając z tej strony, wyrażasz zgodę na
							przetwarzanie Twoich danych przez nas i naszych partnerów, w tym Google.
						</p>
						<p className="text-sm text-zinc-400">
							Możesz dostosować swoje preferencje dotyczące plików cookie poniżej. Więcej informacji
							znajdziesz w naszej
							<a
								href="/privacy-policy"
								className="mx-1 font-semibold underline hover:text-secondary"
							>
								Polityce Prywatności
							</a>
							oraz w informacji o tym
							<a
								href="https://business.safety.google/privacy/"
								target="_blank"
								rel="noopener noreferrer"
								className="mx-1 font-semibold underline hover:text-secondary"
							>
								jak Google przetwarza Twoje dane
							</a>
							.
						</p>
					</div>
					<button
						aria-label="Zamknij okno cookie"
						onClick={() => {
							rejectAll()
							setShow(false)
						}}
						className="h-5 w-5 flex-shrink-0"
					>
						<HiXMark size={20} className="text-zinc-400 hover:text-white" />
					</button>
				</div>

				<div className="mb-4 space-y-4">
					<div className="flex items-center justify-between">
						<label className="flex items-center">
							<input
								type="checkbox"
								checked={settings.necessary}
								disabled
								className="mr-2 disabled:opacity-50"
							/>
							<div>
								<p className="font-medium">Niezbędne</p>
								<p className="text-sm text-zinc-400">Wymagane do prawidłowego działania strony</p>
							</div>
						</label>
					</div>

					<div className="flex items-center justify-between">
						<label className="flex items-center">
							<input
								type="checkbox"
								checked={settings.analytics_storage}
								onChange={(e) =>
									setSettings((prev) => ({
										...prev,
										analytics_storage: e.target.checked
									}))
								}
								className="mr-2"
							/>
							<div>
								<p className="font-medium">Analityczne</p>
								<p className="text-sm text-zinc-400">
									Pomagają nam zrozumieć, jak użytkownicy korzystają z naszej strony
								</p>
							</div>
						</label>
					</div>

					<div className="flex items-center justify-between">
						<label className="flex items-center">
							<input
								type="checkbox"
								checked={settings.ad_storage}
								onChange={(e) =>
									setSettings((prev) => ({
										...prev,
										ad_storage: e.target.checked,
										ad_user_data: e.target.checked,
										ad_personalization: e.target.checked
									}))
								}
								className="mr-2"
							/>
							<div>
								<p className="font-medium">Marketingowe i Personalizacja</p>
								<p className="text-sm text-zinc-400">
									Używane do wyświetlania spersonalizowanych reklam i treści w oparciu o Twoje
									zainteresowania
								</p>
							</div>
						</label>
					</div>
				</div>

				<div className="flex flex-wrap items-center gap-4">
					<div className="flex-grow" />

					<button
						className="rounded border border-zinc-600 px-4 py-2 text-lg text-zinc-400 transition-colors hover:bg-zinc-700 hover:text-zinc-300 focus-visible:ring-offset-zinc-800"
						onClick={() => rejectAll()}
					>
						Odrzuć wszystkie
					</button>

					<button
						className="btn btn-inverse rounded px-4 py-2 text-lg focus-visible:ring-offset-zinc-800"
						onClick={() => savePreferences()}
					>
						Zapisz wybrane
					</button>

					<button
						className="btn rounded px-4 py-2 text-lg focus-visible:ring-offset-zinc-800"
						onClick={() => acceptAll()}
					>
						Akceptuj wszystkie
					</button>
				</div>
			</div>
		</div>
	)
}
