import { getReviewsSection } from '@/utils/api'
import Link from 'next/link'
import { Review } from '../reviews/Review'
import Carousel from './Carousel'

export default async function Reviews() {
	const { sectionData, reviews } = await getReviewsSection()
	return (
		<section
			className="container mx-auto grid gap-12 px-4 py-12 lg:px-12"
			aria-labelledby="reviewsTitle"
		>
			<h2 id="reviewsTitle" className="title text-primary">
				{sectionData.title}
			</h2>
			<div className="grid gap-8 xl:hidden">
				<Carousel
					array={reviews}
					slideWidths={[
						{ breakpoint: 640, width: 32 * 16 },
						{ breakpoint: 0, width: 16 * 16 }
					]}
				>
					{reviews.map((review, index) => (
						<div key={index}>
							<div className="w-64 snap-start snap-always sm:w-[32rem]">
								<Review key={index} review={review} fullWidth />
							</div>
						</div>
					))}
				</Carousel>
			</div>

			<div className="flex justify-between gap-8 max-xl:hidden">
				{reviews.slice(0, 3).map((review, index) => (
					<Review key={index} review={review} fullWidth />
				))}
			</div>

			<Link
				href="/reviews"
				className="mx-auto w-fit border-b border-primary border-opacity-0 font-medium text-primary duration-300 hover:border-opacity-100"
			>
				{sectionData.buttonText}
			</Link>
		</section>
	)
}
