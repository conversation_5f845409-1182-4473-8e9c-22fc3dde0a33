import { getDevelopersPage } from '@/utils/api'
import { Developer } from './Developer'

export async function generateMetadata() {
	const { title } = await getDevelopersPage()
	return { title }
}

export default async function Staff() {
	const { title, developers } = await getDevelopersPage()

	return (
		<section className="container mx-auto flex min-h-screen flex-col gap-8 px-4 pb-12 lg:px-12">
			<h1 className="title text-gray-800">{title}</h1>

			<div className="flex h-full flex-wrap gap-8">
				{developers.map((developer, index) => (
					<Developer key={index} developer={developer} />
				))}
			</div>
		</section>
	)
}
