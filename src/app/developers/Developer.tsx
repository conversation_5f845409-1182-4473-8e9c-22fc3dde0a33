import { Developer as DeveloperType } from '@/types'
import { getAssetPath } from '@/utils/assets'
import Image from 'next/image'
import {
	FaFacebook,
	FaMedium,
	FaGithub,
	FaInstagram,
	FaLinkedinIn,
	Fa<PERSON>wi<PERSON>,
	FaTiktok
} from 'react-icons/fa'

export const Developer = ({
	developer: { socials, img, fullName, position }
}: {
	developer: DeveloperType
}) => {
	const getSocialIcon = (name: string) => {
		switch (name) {
			case 'facebook':
				return <FaFacebook className="h-4 w-4 transition-all duration-300 group-hover:scale-110" />
			case 'linkedin':
				return <FaLinkedinIn className="h-4 w-4 transition-all duration-300 group-hover:scale-110" />
			case 'instagram':
				return <FaInstagram className="h-4 w-4 transition-all duration-300 group-hover:scale-110" />
			case 'twitter':
				return <FaTwitter className="h-4 w-4 transition-all duration-300 group-hover:scale-110" />
			case 'github':
				return <FaGithub className="h-4 w-4 transition-all duration-300 group-hover:scale-110" />
			case 'medium':
				return <FaMedium className="h-4 w-4 transition-all duration-300 group-hover:scale-110" />
			case 'tiktok':
				return <FaTiktok className="h-4 w-4 transition-all duration-300 group-hover:scale-110" />
			default:
				return null
		}
	}
	return (
		<div className="group flex h-[30rem] w-full flex-col gap-6 rounded-2xl ring-1 ring-gray-200/50 bg-white p-4 shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl sm:h-96 sm:w-72">
			<div className="relative h-2/3 overflow-hidden rounded-xl">
				<div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
				<Image
					src={getAssetPath(img.path)}
					alt="developer"
					width={480}
					height={480}
					className="h-full w-full object-cover object-top transition-all duration-500 ease-out group-hover:scale-110"
				/>
				{socials && (
					<div className="absolute -bottom-16 left-1/2 flex w-fit -translate-x-1/2 scale-75 items-center gap-3 opacity-0 transition-all duration-300 ease-in-out group-hover:bottom-4 group-hover:scale-100 group-hover:opacity-100">
						{socials.map((social) => (
							<a
								key={social.type}
								href={social.url}
								className="group/social invisible grid place-content-center rounded-xl bg-white/95 p-3 text-primary shadow-lg backdrop-blur-sm transition-all duration-300 hover:bg-gradient-to-r hover:from-primary hover:to-secondary hover:text-white hover:scale-110 hover:shadow-xl group-hover:visible"
							>
								{getSocialIcon(social.type)}
							</a>
						))}
					</div>
				)}
			</div>
			<div className="flex flex-col gap-3">
				<h3 className="text-xl font-semibold tracking-tight text-primary transition-colors duration-300 group-hover:text-secondary">{fullName}</h3>
				<div className="h-1 w-12 rounded-full bg-gradient-to-r from-primary to-secondary shadow-sm transition-all delay-150 duration-300 ease-out group-hover:w-24 group-hover:shadow-lg"></div>
				<p className="text-sm font-medium text-gray-600 transition-colors duration-300 group-hover:text-gray-800">{position}</p>
			</div>
		</div>
	)
}
