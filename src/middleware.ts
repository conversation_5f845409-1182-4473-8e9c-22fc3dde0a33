import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
	const requestHeaders = new Headers(request.headers)
	requestHeaders.set('x-url', request.url)
	if (request.nextUrl.pathname === '/staff') {
		return NextResponse.redirect(new URL('/staff/teachers', request.url))
	}
	if (request.nextUrl.pathname === '/techdaywarszawa') {
		return NextResponse.redirect(new URL('/TechDayWarszawa', request.url))
	}
	if (request.nextUrl.pathname.startsWith('/pl')) {
		return NextResponse.redirect(
			new URL(request.nextUrl.pathname.split('/pl')[1] + '/', request.url)
		)
	}

	return NextResponse.next({
		request: {
			headers: requestHeaders
		}
	})
}

export const config = {
	matcher: ['/((?!api|robots.txt|sitemap.xml|_next/static|_next/image|favicon.ico|assets).*)']
}
