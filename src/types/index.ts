type Collection<T extends {}> = {
	_id: string
	_created: number
	_modified: number
	_cby: string
	_mby: string
	_state: string
} & T

type Singleton<T extends {}> = Collection<{
	_model: string
}> &
	T

type Image = {
	path: string
	description: string
	colors: string[]
	width: number
	height: number
}

export type City = Collection<{
	city: string
	label: string
}>

export type Differences = Collection<{
	title: string
	differences: {
		icon: Image
		description: string
	}[]
}>

export type Review = Collection<{
	fullName: string
	review: string
	position: {
		type: 'student' | 'parent'
		label: string
	}
	img: Image
	videoUrl?: string
	shortReview?: string
}>

export type Location = Collection<{
	city: string
	address?: string
	tuition?: string
	recruitmentFee?: string
	vacancies?: number
	commencementDate?: string
	recruitmentEndDate?: string
	description: string
	position: {
		top: number
		left: number
	}
}>

export type Gallery = Collection<{
	img: Image
}>

export type Post = Collection<{
	title: string
	keywords: string
	img: Image
	description: string
	content: string
	author?: string
	authorSocial?: string
	date: string
	location:
		| 'warszawa'
		| 'lublin'
		| 'kraków'
		| 'wrocław'
		| 'poznań'
		| 'szczecin'
		| 'katowice'
		| 'bydgoszcz'
		| 'łódź'
		| 'rzeszów'
		| 'białystok'
		| 'toruń'
		| 'trójmiasto'
	type: 'article' | 'announcement'
	slug: string
	disableInfoModal?: boolean
}>

export type Staff = Collection<{
	fullName: string
	jobTitle: string
	city?: string[]
	position: 'teacher' | 'council'
	socials?: {
		type:
			| 'facebook'
			| 'instagram'
			| 'youtube'
			| 'linkedin'
			| 'twitter'
			| 'github'
			| 'tiktok'
			| 'medium'
		url: string
	}[]
	img: Image
}>

export type WhyTechni = Collection<{
	slideTitle: string
	slideDescription: string
	slideShortcut: string
	img: Image
}>

export type HeaderLinks = Collection<{
	name: string
	href?: string
	paths: {
		name: string
		href: string
		type: 'internal' | 'external' | 'anchor'
	}[]
	type: 'internal' | 'external' | 'anchor'
}>

export type CustomPage = Collection<{
	title: string
	keywords: string
	slug: string
	page: string[]
	code: string
	pageSchema?: string
	description?: string
	trackForm?: boolean
	disableInfoModal?: boolean
	transparentHeader?: boolean
}>

export type TitleDescription = Singleton<{
	title: string
	description: string
}>

export type FAQPage = Singleton<{
	title: string
	questions: {
		question: string
		answer: string
	}[]
}>

export type StaffSection = Singleton<{
	title: string
	description: string
	backgroundImg: Image
	fullStaffText: string
}>

export type ApplyToday = Singleton<{
	title: string
	cta: string
	vacanciesInfo: string
	vacancies: number
}>

export type Title = Singleton<{
	title: string
}>

export type Description = Singleton<{
	description: string
}>

export type OnlineLessons = Singleton<{
	title: string
	description: string[]
	url: string
	buttonText: string
}>

export type TitleImage = Singleton<{
	title: string
	img: Image[]
}>

export type RecruitmentSection = Singleton<{
	title: string
	steps: {
		title: string
		description: string
	}[]
}>

export type HeroText = Singleton<{
	content: {
		topBlock: string
		mainText: string
		bottomBlock: string
		anchorText?: string
		anchorLink?: string
		backgroundImg: Image
	}[]
}>

export type Benefits = Singleton<{
	title: string
	subtitle: string
	description: string
	benefits: string[]
	buttonText: string
	img: Image
}>

export type Welcome = Singleton<{
	title: string
	description: string
	url: string
}>

export type ReviewsSection = Singleton<{
	title: string
	buttonText: string
}>

export type Partners = Singleton<{
	title: string
	partners: {
		name: string
		url: string
		logo: Image
	}[]
}>

export type FAQDescription = Singleton<{
	description: string
}>

export type GallerySection = Singleton<{
	title: string
	anchorText: string
}>

export type CookiePopup = Singleton<{
	description: string
	buttonText: string
}>

export type Modal = Singleton<{
	isEnabled: boolean
	title: string
	subtitle: string
	buttonText: string
	cities: {
		name: string
		date: string
		address: string
	}[]
	positionPercentage: number
	inputDescription: string
	acceptTerms: string
	successMessage: string
	bottomInfo: string
}>

export type ContactSection = Singleton<{
	phone: string
	email: string
	callText: string
	emailText: string
	socials: {
		type:
			| 'facebook'
			| 'instagram'
			| 'youtube'
			| 'linkedin'
			| 'twitter'
			| 'github'
			| 'tiktok'
			| 'medium'
		url: string
	}[]
}>

export type StaffPage = Singleton<{
	title: string
	anchorText: string
}>

export type Footer = Singleton<{
	citiesInfo: string
	menuTitle: string
	importantLinksTitle: string
	contactTitle: string
	menuLinks: {
		name: string
		href: string
		type: 'internal' | 'external' | 'anchor'
	}[]
	importantLinks: {
		name: string
		href: string
		type: 'internal' | 'external' | 'anchor'
	}[]
	phoneNumbers: {
		description: string
		number: string
	}[]
	emails: string[]
	socials: {
		type:
			| 'facebook'
			| 'instagram'
			| 'youtube'
			| 'linkedin'
			| 'twitter'
			| 'github'
			| 'tiktok'
			| 'medium'
		url: string
	}[]
	devInfo: string
	devPageUrl: string
}>

export type BlogPage = Singleton<{
	title: string
	noPostsTitle: string
	noPostsSubtitle: string
}>

export type ContactPage = Singleton<{
	title: string
	contact: {
		type: string
		data: string
		description: string
	}[]
}>

export type ReviewsPage = Singleton<{
	title: string
	metaDescription: string
	studentsSubtitle: string
	parentsSubtitle: string
}>

export type CommonTexts = Singleton<{
	shareText: string
	readMoreText: string
	authorText: string
}>

export type Developer = {
	fullName: string
	position: string
	img: Image
	socials: {
		type: 'facebook' | 'instagram' | 'linkedin' | 'twitter' | 'github'
		url: string
	}[]
}

export type DevPage = Singleton<{
	title: string
	developers: Developer[]
}>

export type Metadata = Singleton<{
	title: string
	template: string
	description: string
	favicon: Image
	ogImage: Image
	keywords: string
}>

// TEMPORARY

export type SpectaclePage = Singleton<{
	creators: {
		fullName: string
		bio: string
		img: Image
	}[]
	actors: {
		fullName: string
		img: Image
	}[]
	partners: {
		name: string
		url: string
		logo: Image
	}[]
	title: string
	description: string
	keywords: string
	otherNames: string
}>
