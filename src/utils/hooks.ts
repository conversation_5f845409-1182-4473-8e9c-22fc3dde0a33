import { Dispatch, SetStateAction, useEffect, useState } from 'react'

export function useLocalStorage<T>(key: string, initialValue: T): [T, Dispatch<SetStateAction<T>>] {
	const fromLocal = () => {
		if (typeof window === 'undefined') {
			return initialValue
		}
		try {
			const item = window.localStorage.getItem(key)
			return item ? (JSON.parse(item) as T) : initialValue
		} catch (error) {
			console.error(error)
			return initialValue
		}
	}
	const [storedValue, setStoredValue] = useState(fromLocal())
	// We will use this flag to trigger the reading from localStorage
	const [firstLoadDone, setFirstLoadDone] = useState(false)

	// Use an effect hook in order to prevent SSR inconsistencies and errors.
	// This will update the state with the value from the local storage after
	// the first initial value is applied.
	useEffect(() => {
		// 	// Set the value from localStorage
		setStoredValue(fromLocal())
		// 	// First load is done
		setFirstLoadDone(true)
	}, [initialValue, key])
	// Instead of replacing the setState function, react to changes.
	// Whenever the state value changes, save it in the local storage.
	useEffect(() => {
		// If it's the first load, don't store the value.
		// Otherwise, the initial value will overwrite the local storage.
		if (!firstLoadDone) {
			return
		}

		try {
			if (typeof window !== 'undefined') {
				window.localStorage.setItem(key, JSON.stringify(storedValue, null, 2))
			}
		} catch (error) {
			console.log(error)
		}
	}, [storedValue, firstLoadDone, key])

	// Return the original useState functions
	return [storedValue, setStoredValue]
}

interface UseAutosaveProps<T> {
	data: T
	onSave: (data: T) => void
	enabled?: boolean
	interval?: number
}

export function useAutosave<T>({
	data,
	onSave,
	enabled = false,
	interval = 5000
}: UseAutosaveProps<T>) {
	useEffect(() => {
		if (enabled) {
			const intervalId = setInterval(() => {
				console.log('saving')
				onSave(data)
			}, interval)
			return () => clearInterval(intervalId)
		}
	}, [data, enabled, interval, onSave])
}
