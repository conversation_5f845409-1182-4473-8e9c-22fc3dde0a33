import {
	FAQPage,
	HeroText,
	Welcome,
	Location,
	CookiePopup,
	Benefits,
	TitleImage,
	OnlineLessons,
	TitleDescription,
	ReviewsSection,
	HeaderLinks,
	Staff,
	Title,
	WhyTechni,
	Partners,
	Differences,
	ApplyToday,
	StaffSection,
	ContactSection,
	Footer,
	GallerySection,
	CustomPage,
	Post,
	BlogPage,
	ContactPage,
	CommonTexts,
	Review,
	ReviewsPage,
	Gallery,
	RecruitmentSection,
	StaffPage,
	DevPage,
	Metadata,
	City,
	Modal,
	SpectaclePage
} from '@/types'
import { i18n } from './i18n'

const fetchFromApi = async <T>(
	method: 'GET' | 'POST',
	endpoint: string,
	options?: {
		filter?: {} | string
		fields?: {} | string
		limit?: number
		sort?: {}
		skip?: number
	}
): Promise<T> => {
	const locale = i18n.defaultLocale

	const url = new URL(`${process.env.API_URL}${endpoint}`)
	url.searchParams.set('locale', locale)
	if (options?.filter) url.searchParams.set('filter', JSON.stringify(options.filter))
	if (options?.limit) url.searchParams.set('limit', String(options.limit))
	if (options?.skip) url.searchParams.set('skip', String(options.skip))
	if (options?.sort) url.searchParams.set('sort', JSON.stringify(options.sort))
	if (options?.fields) url.searchParams.set('fields', JSON.stringify(options.fields))
	const response = await fetch(url, {
		method,
		next: {
			revalidate: 60 * 10
		}
	})
	const data = await response.json()
	return data
}

export const getLocations = async () => {
	const locale = i18n.defaultLocale
	const [data, title] = await Promise.all([
		fetchFromApi<Location[]>('GET', '/items/locations'),
		fetchFromApi<Title>('GET', '/item/locationsSection')
	])
	const replaceDescription = (location: Location) => {
		return location.description
			.replace('{tuition}', location.tuition || '')
			.replace('{recruitmentFee}', location.recruitmentFee || '')
			.replace('{vacancies}', String(location.vacancies) || '')
			.replace('{commencementDate}', location.commencementDate || '')
			.replace(
				'{recruitmentEndDate}',
				new Date(location.recruitmentEndDate || '').toLocaleDateString(locale)
			)
	}
	const locations = data.map((location: Location) => ({
		...location,
		description: replaceDescription(location)
	})) as Location[]
	return { locations, title: title.title }
}

export const getWhyTechni = async () => {
	const [titleData, steps] = await Promise.all([
		fetchFromApi<Title>('GET', '/item/whyTechniTitle'),
		fetchFromApi<WhyTechni[]>('GET', '/items/whyTechni')
	])
	return { title: titleData.title, steps }
}

export const getHeader = async () => {
	const [links, buttonText] = await Promise.all([
		fetchFromApi<HeaderLinks[]>('GET', '/items/headerLinks'),
		fetchFromApi<Title>('GET', '/item/headerApplyButton')
	])
	return { links, buttonText: buttonText.title }
}

export const getTechniDifferences = async () => {
	const [titleData, differences] = await Promise.all([
		fetchFromApi<Title>('GET', '/item/techniDifferencesSection'),
		fetchFromApi<Differences[]>('GET', '/items/differencesSections')
	])
	return { title: titleData.title, differences }
}

export const getStaffSection = async () => {
	const [sectionData, staff] = await Promise.all([
		fetchFromApi<StaffSection>('GET', '/item/staffSection'),
		fetchFromApi<Staff[]>('GET', '/items/staff', {
			limit: 10,
			filter: {
				position: 'Teacher'
			}
		})
	])
	return { sectionData, staff }
}

export const getHeroSection = () => fetchFromApi<HeroText>('GET', '/item/heroSection')

export const getWelcomeSection = () => fetchFromApi<Welcome>('GET', '/item/welcomeSection')

export const getCookiePopup = () => fetchFromApi<CookiePopup>('GET', '/item/cookiePopup')

export const getProgrammingBenefits = () =>
	fetchFromApi<Benefits>('GET', '/item/programmingBenefits')

export const getPromises = () => fetchFromApi<TitleImage>('GET', '/item/promises')

export const getStaffMembers = (type: 'teachers' | 'council' = 'teachers', city?: string) =>
	fetchFromApi<Staff[]>('GET', '/items/staff', {
		filter: {
			position: type === 'teachers' ? 'Teacher' : 'Council/Administration Member',
			city: type === 'teachers' ? city : undefined
		}
	})

export const getFAQDescription = () => fetchFromApi<TitleDescription>('GET', '/item/faqDescription')

export const getRecruitmentSection = () =>
	fetchFromApi<RecruitmentSection>('GET', '/item/recruitmentSection')

export const getOnlineLessons = () => fetchFromApi<OnlineLessons>('GET', '/item/onlineLessons')

export const getAboutSchool = async () => {
	const [sectionData, posts] = await Promise.all([
		fetchFromApi<TitleDescription>('GET', '/item/aboutSchool'),
		fetchFromApi<Post[]>('GET', '/items/posts', {
			limit: 3,
			filter: { type: 'article' },
			fields: {
				title: true,
				slug: true,
				img: true,
				description: true,
				date: true,
				location: true,
				type: true
			},
			sort: {
				date: -1
			}
		})
	])
	return { sectionData, posts }
}

export const getReviewsSection = async () => {
	const [sectionData, reviews] = await Promise.all([
		fetchFromApi<ReviewsSection>('GET', '/item/reviewsSection'),
		fetchFromApi<Review[]>('GET', '/items/reviews', { limit: 3 })
	])
	return { sectionData, reviews }
}

export const getReviewsPage = async () => {
	const [pageData, reviews] = await Promise.all([
		fetchFromApi<ReviewsPage>('GET', '/item/reviewsPage'),
		fetchFromApi<Review[]>('GET', '/items/reviews')
	])
	return { pageData, reviews }
}

export const getPartners = () => fetchFromApi<Partners>('GET', '/item/partners')

export const getFAQPage = () => fetchFromApi<FAQPage>('GET', '/item/faqPage')

export const getApplyToday = async () => fetchFromApi<ApplyToday>('GET', '/item/applyToday')

export const getCustomPage = async (slug: string) => {
	const data = await fetchFromApi<CustomPage[]>('GET', '/items/customPages', {
		filter: { slug }
	})
	return data[0]
}

export const getCustomPages = async () => fetchFromApi<CustomPage[]>('GET', '/items/customPages')

export const getCustomPagesInfo = async () =>
	fetchFromApi<
		{
			slug: string
			transparentHeader?: boolean
			pageSchema?: string
			_modified: number
			_created: number
		}[]
	>('GET', '/items/customPages', {
		fields: {
			slug: true,
			transparentHeader: true,
			pageSchema: true,
			_modified: true,
			_created: true
		}
	})

export const getContactSection = async () =>
	fetchFromApi<ContactSection>('GET', '/item/contactSection')

export const getFooter = async () => fetchFromApi<Footer>('GET', `/item/footer`)

const PAGE_SIZE = 20

export const getAmountOfPosts = async (type: 'announcement' | 'article') => {
	const data = await fetchFromApi<Post[]>('GET', '/items/posts', {
		filter: { type },
		fields: { '': true }
	})
	return data.length
}

export const getArticles = async (page?: number) =>
	fetchFromApi<Post[]>('GET', '/items/posts', {
		fields: {
			title: true,
			slug: true,
			img: true,
			description: true,
			date: true,
			location: true,
			type: true
		},
		filter: { type: 'article' },
		sort: {
			date: -1
		},
		limit: page ? page * PAGE_SIZE : undefined
	})

export const getAnnouncements = async (page?: number) =>
	fetchFromApi<Post[]>('GET', '/items/posts', {
		fields: {
			title: true,
			slug: true,
			img: true,
			description: true,
			date: true,
			location: true,
			type: true
		},
		filter: { type: 'announcement' },
		sort: {
			date: -1
		},
		limit: page ? page * PAGE_SIZE : undefined
	})

export const getPost = async (slug: string) => {
	const data = await fetchFromApi<Post[]>('GET', '/items/posts', {
		filter: { slug }
	})
	return data[0]
}

export const getBlogPage = async (page?: number) => {
	const [pageData, posts] = await Promise.all([
		fetchFromApi<BlogPage>('GET', '/item/blogPageData'),
		getArticles(page ?? 1)
	])
	return { pageData, posts }
}
export const getBlogLayout = async () => fetchFromApi<BlogPage>('GET', '/item/blogPageData')

export const getAnnouncementsPage = async (page?: number) => {
	const [pageData, posts] = await Promise.all([
		fetchFromApi<BlogPage>('GET', '/item/announcementsPageData'),
		getAnnouncements(page ?? 1)
	])
	return { pageData, posts }
}

export const getAnnouncementsLayout = async () =>
	fetchFromApi<BlogPage>('GET', '/item/announcementsPageData')

export const getContactPage = async () => fetchFromApi<ContactPage>('GET', '/item/contactPage')

export const getCommonTexts = async () => fetchFromApi<CommonTexts>('GET', '/item/commonTexts')

export const getGalleryPage = async () => {
	const [titleData, images] = await Promise.all([
		fetchFromApi<Title>('GET', '/item/gallerySection'),
		fetchFromApi<Gallery[]>('GET', '/items/gallery', {
			sort: {
				_created: -1
			}
		})
	])
	return { title: titleData.title, images: images }
}

export const getGallerySection = async () => {
	const [sectionData, images] = await Promise.all([
		fetchFromApi<GallerySection>('GET', '/item/gallerySection'),
		fetchFromApi<Gallery[]>('GET', '/items/gallery', {
			limit: 8,
			sort: {
				_created: -1
			}
		})
	])
	return { sectionData, images }
}

export const getTeachersPage = async () => {
	const [{ teachers: sectionData }, members] = await Promise.all([
		fetchFromApi<{
			teachers: StaffPage
		}>('GET', '/item/staffPage', { filter: { teachers: true } }),
		getStaffMembers('teachers')
	])
	return { sectionData, members }
}

export const getCouncilPage = async () => {
	const [{ council: sectionData }, members] = await Promise.all([
		fetchFromApi<{
			council: StaffPage
		}>('GET', '/item/staffPage', { filter: { council: true } }),
		getStaffMembers('council')
	])
	return { sectionData, members }
}

export const getDevelopersPage = async () => fetchFromApi<DevPage>('GET', '/item/developersPage')

export const getMetaData = async () => fetchFromApi<Metadata>('GET', '/item/mainMetadata')

export const getCities = async () => fetchFromApi<City[]>('GET', '/items/cities')

export const getInfoModal = async () => fetchFromApi<Modal>('GET', '/item/infoModal')

// TEMPORARY

export const getSpectaclePage = async () =>
	fetchFromApi<SpectaclePage>('GET', '/item/spectaclePage')
